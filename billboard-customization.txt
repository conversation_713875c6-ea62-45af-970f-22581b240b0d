<style>

    /* Section headers */
    .design-section-title {
        font-size: 24px;
        font-weight: 600;
        margin: 30px 0 20px;
        color: #333;
        text-align: center;
    }

    /* Billboard preview container */
    .billboard-preview-container {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .billboard-canvas-container {
        position: relative;
        width: 100%;
        max-width: 800px;
        margin: 0 auto;
        background: #000;
        border-radius: 8px;
        overflow: hidden;
    }

    #billboardCanvas {
        width: 100%;
        height: auto;
        display: block;
        background: #000;
    }

    /* Template selection */
    .template-selection {
        display: flex;
        justify-content: center;
        gap: 10px;
        margin-bottom: 30px;
        flex-wrap: wrap;
    }

    .template-option {
        width: 120px;
        height: 80px;
        border: 3px solid transparent;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;
        overflow: hidden;
        position: relative;
    }

    .template-option.active {
        border-color: #0D7EE8;
        box-shadow: 0 0 15px rgba(13,126,232,0.3);
    }

    .template-option img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .template-option::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.3);
        opacity: 0;
        transition: opacity 0.3s;
    }

    .template-option:hover::after {
        opacity: 1;
    }
    
    /* Text editor section */
    .text-editor-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .text-line-container {
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .text-line-input {
        flex: 1;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 16px;
        font-weight: bold;
        text-transform: uppercase;
    }

    .text-controls {
        display: flex;
        gap: 5px;
    }

    /* Color picker dropdown */
    .color-picker-dropdown {
        position: relative;
        width: 36px;
        height: 36px;
    }

    .color-picker-btn {
        width: 100%;
        height: 100%;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        position: relative;
        background: #ffffff;
    }

    .color-picker-btn::after {
        content: '▼';
        position: absolute;
        right: 2px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 8px;
        color: white;
        text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
    }

    .color-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        background: #f5f5f5;
        border: 1px solid #ddd;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        z-index: 1000;
        display: none;
        padding: 20px;
        width: 420px;
    }

    .color-dropdown.show {
        display: block;
    }

    .color-palette {
        display: grid;
        grid-template-columns: repeat(10, 20px);
        gap: 3px;
        margin-bottom: 20px;
        padding: 15px;
        background: white;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
    }

    .color-swatch {
        width: 20px;
        height: 20px;
        border-radius: 3px;
        cursor: pointer;
        border: 1px solid #ddd;
        transition: all 0.2s ease;
        position: relative;
    }

    .color-swatch:hover {
        transform: scale(1.1);
        border-color: #999;
        box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        z-index: 1;
    }

    .color-swatch.selected {
        border: 2px solid #333;
        transform: scale(1.05);
    }

    .color-swatch.checkmark {
        background: #00ff00 !important;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: #000;
        font-size: 14px;
    }

    /* Advanced Color Picker Components */
    .color-picker-advanced {
        display: flex;
        gap: 15px;
        background: white;
        border-radius: 8px;
        padding: 15px;
        border: 1px solid #e0e0e0;
    }

    .color-gradient-area {
        width: 240px;
        height: 180px;
        position: relative;
        background: linear-gradient(to right, #fff, #ff0000);
        border: 1px solid #ccc;
        cursor: crosshair;
        border-radius: 4px;
    }

    .color-gradient-area::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, transparent, #000);
        pointer-events: none;
        border-radius: 3px;
    }

    .color-gradient-picker {
        position: absolute;
        width: 12px;
        height: 12px;
        border: 2px solid #fff;
        border-radius: 50%;
        box-shadow: 0 0 4px rgba(0,0,0,0.6);
        transform: translate(-50%, -50%);
        pointer-events: none;
    }

    .hue-slider-container {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .hue-slider {
        width: 24px;
        height: 180px;
        background: linear-gradient(to bottom,
            #ff0000 0%,
            #ffff00 16.66%,
            #00ff00 33.33%,
            #00ffff 50%,
            #0000ff 66.66%,
            #ff00ff 83.33%,
            #ff0000 100%);
        border: 1px solid #ccc;
        position: relative;
        cursor: pointer;
        border-radius: 4px;
    }

    .hue-slider-handle {
        position: absolute;
        width: 28px;
        height: 8px;
        background: #fff;
        border: 1px solid #333;
        left: -2px;
        transform: translateY(-50%);
        pointer-events: none;
        border-radius: 2px;
    }

    .color-preview-section {
        display: flex;
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }

    .color-preview-large {
        width: 60px;
        height: 60px;
        border: 1px solid #ccc;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .color-picker-buttons {
        display: flex;
        gap: 10px;
        flex-direction: column;
    }

    .color-picker-btn-small {
        padding: 8px 16px;
        border: 1px solid #ddd;
        background: #fff;
        border-radius: 4px;
        cursor: pointer;
        font-size: 13px;
        transition: all 0.2s;
        min-width: 60px;
    }

    .color-picker-btn-small:hover {
        background: #f8f9fa;
        border-color: #999;
    }

    .color-picker-btn-small.cancel {
        color: #dc3545;
        border-color: #dc3545;
    }

    .color-picker-btn-small.cancel:hover {
        background: #f8d7da;
    }

    .color-picker-btn-small.choose {
        color: #28a745;
        border-color: #28a745;
        font-weight: 600;
    }

    .color-picker-btn-small.choose:hover {
        background: #d4edda;
    }

    /* Image Resize Dialog */
    .image-resize-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 2000;
        display: none;
        align-items: center;
        justify-content: center;
    }

    .image-resize-content {
        background: white;
        border-radius: 12px;
        padding: 20px;
        max-width: 90vw;
        max-height: 90vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 15px;
        position: relative;
    }

    .image-resize-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
    }

    .image-resize-canvas {
        position: relative;
        display: inline-block;
        max-width: 600px;
        max-height: 400px;
    }

    .resize-image {
        max-width: 600px;
        max-height: 400px;
        display: block;
        user-select: none;
        pointer-events: none;
    }

    .crop-overlay {
        position: absolute;
        border: 2px solid #00bcd4;
        background: rgba(0, 188, 212, 0.2);
        cursor: move;
        min-width: 50px;
        min-height: 50px;
        box-sizing: border-box;
    }

    .crop-overlay::before {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        right: 2px;
        bottom: 2px;
        border: 1px dashed #ffffff;
        pointer-events: none;
    }

    .crop-handle {
        position: absolute;
        width: 12px;
        height: 12px;
        background: #00bcd4;
        border: 2px solid white;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .crop-handle.nw { top: -6px; left: -6px; cursor: nw-resize; }
    .crop-handle.ne { top: -6px; right: -6px; cursor: ne-resize; }
    .crop-handle.sw { bottom: -6px; left: -6px; cursor: sw-resize; }
    .crop-handle.se { bottom: -6px; right: -6px; cursor: se-resize; }

    .image-resize-buttons {
        display: flex;
        gap: 15px;
    }

    .resize-btn {
        padding: 12px 30px;
        border: none;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        min-width: 100px;
    }

    .resize-btn.cancel {
        background: #28a745;
        color: white;
    }

    .resize-btn.cancel:hover {
        background: #218838;
    }

    .resize-btn.done {
        background: #007bff;
        color: white;
    }

    .resize-btn.done:hover {
        background: #0056b3;
    }

    .settings-btn {
        width: 36px;
        height: 36px;
        border: 1px solid #ddd;
        background: #f8f9fa;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s;
    }

    .settings-btn:hover {
        background: #e9ecef;
        border-color: #0D7EE8;
    }

    /* Font selector */
    .font-selector {
        margin-bottom: 20px;
    }

    .font-dropdown {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        background: white;
    }
    
    /* Design options section */
    .design-options-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .design-options-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .design-option {
        display: flex;
        flex-direction: column;
    }

    .design-option-label {
        font-weight: 600;
        margin-bottom: 10px;
        color: #333;
    }

    .color-picker-container {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .color-preview {
        width: 40px;
        height: 40px;
        border-radius: 6px;
        border: 2px solid #ddd;
        cursor: pointer;
        transition: all 0.2s;
    }

    .color-preview:hover {
        border-color: #0D7EE8;
        transform: scale(1.05);
    }

    .color-value {
        font-family: monospace;
        font-size: 14px;
        color: #666;
    }

    /* Upload section */
    .upload-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .upload-container {
        border: 2px dashed #ddd;
        border-radius: 8px;
        padding: 40px 20px;
        text-align: center;
        transition: all 0.3s;
        cursor: pointer;
        background: #fafafa;
    }

    .upload-container:hover {
        border-color: #0D7EE8;
        background-color: rgba(13,126,232,0.05);
    }

    .upload-icon {
        font-size: 48px;
        color: #999;
        margin-bottom: 15px;
    }

    .upload-text {
        color: #666;
        margin-bottom: 10px;
        font-weight: 500;
    }

    .upload-subtext {
        color: #999;
        font-size: 14px;
    }

    /* Validation messages */
    .validation-message {
        margin-top: 10px;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
    }

    .validation-message.error {
        background-color: #fee;
        color: #c33;
        border: 1px solid #fcc;
    }

    .validation-message.warning {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .validation-message.success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    /* Character counter */
    .character-counter {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
        text-align: right;
    }

    .character-counter.warning {
        color: #856404;
    }

    .character-counter.error {
        color: #c33;
    }
    
    /* Background graphics section */
    .background-graphics {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 10px;
        margin-top: 10px;
    }

    .background-graphic-item {
        aspect-ratio: 1;
        border: 2px solid transparent;
        border-radius: 6px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.2s;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #666;
        text-align: center;
    }

    .background-graphic-item:hover {
        border-color: #0D7EE8;
        transform: scale(1.05);
    }

    .background-graphic-item.selected {
        border-color: #0D7EE8;
        background: rgba(13,126,232,0.1);
    }

    .background-graphic-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* Font settings modal */
    .font-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.7);
        z-index: 1000;
        display: none;
        align-items: center;
        justify-content: center;
    }

    .font-modal-content {
        background: white;
        border-radius: 8px;
        padding: 30px;
        width: 400px;
        max-width: 90%;
    }

    .font-setting-row {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .font-setting-label {
        width: 120px;
        font-weight: 600;
        color: #333;
    }

    .font-setting-control {
        flex: 1;
    }

    .font-dropdown, .font-size-dropdown {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    .shadow-checkbox {
        width: 20px;
        height: 20px;
    }

    .shadow-width-slider {
        width: 100%;
        margin-top: 10px;
    }

    .apply-all-checkbox {
        margin: 20px 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .apply-all-checkbox label {
        color: #000000 !important;
        font-weight: 500;
    }

    .modal-buttons {
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-top: 30px;
    }

    .modal-btn {
        padding: 12px 30px;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
    }

    .modal-btn.apply {
        background: #007bff;
        color: white;
    }

    .modal-btn.apply:hover {
        background: #0056b3;
    }

    .modal-btn.cancel {
        background: #007bff;
        color: white;
    }

    .modal-btn.cancel:hover {
        background: #0056b3;
    }

    /* Submit button */
    .submit-container {
        text-align: center;
        margin: 30px 0;
    }

    .submit-button {
        background-color: #0D7EE8;
        color: white;
        border: none;
        padding: 15px 40px;
        font-size: 16px;
        border-radius: 6px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.2s;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .submit-button:hover {
        background-color: #0A6AC7;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(13,126,232,0.3);
    }
    
    /* No templates message */
    .no-templates-message {
        text-align: center;
        padding: 60px 20px;
        color: #666;
        font-size: 16px;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 12px;
        margin: 20px 0;
        border: 2px dashed #ddd;
        min-height: 200px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .no-templates-message h3 {
        color: #333;
        margin-bottom: 15px;
        font-size: 24px;
        font-weight: 600;
    }

    .no-templates-message p {
        margin: 0;
        font-size: 16px;
        color: #666;
        max-width: 400px;
        line-height: 1.5;
    }

    /* Purpose display styling */
    #purposeDisplay {
        background: linear-gradient(135deg, #0D7EE8, #0A6AC7);
        color: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(13,126,232,0.2);
    }
</style>

<!-- Anniversary CSS -->
<style>
    /* Anniversary Templates Styling */
    .anniversary-templates-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }

    .anniversary-template-card {
        border: 3px solid transparent;
        border-radius: 12px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .anniversary-template-card:hover {
        border-color: #0D7EE8;
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(13,126,232,0.2);
    }

    .anniversary-template-card.selected {
        border-color: #0D7EE8;
        background: rgba(13,126,232,0.05);
    }
</style>

<!-- Benefit CSS -->
<style>
    /* Benefit Templates Styling */
    .benefit-templates-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }

    .benefit-template-card {
        border: 3px solid transparent;
        border-radius: 12px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .benefit-template-card:hover {
        border-color: #0D7EE8;
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(13,126,232,0.2);
    }

    .benefit-template-card.selected {
        border-color: #0D7EE8;
        background: rgba(13,126,232,0.05);
    }



    /* UNIVERSAL TEMPLATE STYLING - Works for any purpose */
    .universal-templates-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }

    .universal-template-card {
        border: 3px solid transparent;
        border-radius: 12px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .universal-template-card:hover {
        border-color: #0D7EE8;
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(13,126,232,0.2);
    }

    .universal-template-card.selected {
        border-color: #0D7EE8;
        background: rgba(13,126,232,0.05);
    }

    .universal-template-preview {
        height: 200px;
        overflow: hidden;
        position: relative;
        background: #000;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        text-align: center;
        padding: 20px;
    }

    .universal-template-content {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .universal-template-text {
        margin: 4px 0;
        font-weight: bold;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    }

    .universal-template-label {
        padding: 12px;
        text-align: center;
        font-weight: 600;
        color: #333;
        background: #f8f9fa;
        border-top: 1px solid #eee;
    }

    .template-preview {
        height: 200px;
        overflow: hidden;
        position: relative;
    }

    /* Anniversary template background images */
    .anniversary-template-card[data-template="anniversary-template-1"] .template-preview {
        background-image: url('https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-1.png');
        background-size: cover;
        background-position: center;
    }

    .anniversary-template-card[data-template="anniversary-template-2"] .template-preview {
        background-image: url('https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-2.png');
        background-size: cover;
        background-position: center;
    }

    .anniversary-template-card[data-template="anniversary-template-3"] .template-preview {
        background-image: url('https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-3.png');
        background-size: cover;
        background-position: center;
    }

    /* Benefit template background images */
    .benefit-template-card[data-template="benefit-template-1"] .template-preview {
        background-image: url('https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-3.png');
        background-size: cover;
        background-position: center;
    }

    .benefit-template-card[data-template="benefit-template-2"] .template-preview {
        background-color: #000000;
    }

    .benefit-template-card[data-template="benefit-template-3"] .template-preview {
        background-image: url('https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-5.png');
        background-size: cover;
        background-position: center;
    }

    .template-preview {
        height: 200px;
        overflow: hidden;
        position: relative;
    }

    .template-layout.horizontal-split {
        display: flex;
        height: 100%;
        width: 100%;
    }

    .text-section {
        flex: 1;
        padding: 15px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        background: transparent;
        color: white;
        position: relative;
    }

    .template-field-text {
        font-size: 14px;
        font-weight: bold;
        margin: 2px 0;
        text-align: center;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    }

    /* Benefit Template 2 specific styles */
    .template-layout.full-text {
        display: flex;
        height: 100%;
        width: 100%;
    }

    .text-section.full-width {
        width: 100%;
        padding: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .template-field-text.red-text {
        color: #ff0000 !important;
    }

    .template-field-text.white-text {
        color: #ffffff !important;
    }

    .template-field-text.large {
        font-size: 18px !important;
        font-weight: 900 !important;
        letter-spacing: 1px;
    }

    .template-field-text.script {
        font-family: 'Dancing Script', cursive !important;
        font-style: italic;
        font-size: 16px !important;
    }

    .template-field-text.bold {
        font-weight: 700 !important;
        font-size: 15px !important;
    }

    .template-field-text.medium {
        font-size: 13px !important;
        font-weight: 700 !important;
    }

    .image-section {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        background: transparent;
    }

    .image-section.full-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .image-section.placeholder-space {
        background: transparent;
    }

    .upload-placeholder {
        text-align: center;
        color: #666;
    }

    .upload-placeholder .upload-icon {
        font-size: 24px;
        margin-bottom: 5px;
    }

    .upload-placeholder .upload-text {
        font-size: 12px;
        font-weight: 500;
    }

    .image-section.centered-image {
        padding: 20px;
    }

    .image-section.centered-image img {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    }

    .template-label {
        padding: 12px;
        text-align: center;
        font-weight: 600;
        color: #333;
        background: #f8f9fa;
        border-top: 1px solid #eee;
    }




    /* Interactive Template Editor */
    .template-editor-container {
        display: none;
        background: white;
        border-radius: 12px;
        padding: 30px;
        margin: 20px 0;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border: 2px solid #0D7EE8;
    }

    .template-editor-container.active {
        display: block;
    }

    .editor-canvas-container {
        position: relative;
        width: 100%;
        max-width: 800px;
        margin: 0 auto 30px;
        background: #000;
        border-radius: 8px;
        overflow: hidden;
        aspect-ratio: 2/1; /* Fixed 2:1 aspect ratio */
    }

    .editor-canvas {
        width: 100%;
        height: 100%;
        display: block;
        background: #000;
    }

    .editor-controls {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-top: 20px;
    }

    .text-editing-panel {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
    }

    .image-editing-panel {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
    }

    .background-graphic-button {
        background: #0D7EE8;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 6px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        margin-bottom: 15px;
        width: 100%;
    }

    .background-graphic-button:hover {
        background: #0A6AC7;
        transform: translateY(-1px);
    }

    .background-graphic-dialog {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.7);
        z-index: 1000;
        display: none;
        align-items: center;
        justify-content: center;
    }

    .background-graphic-dialog.show {
        display: flex;
    }

    .dialog-content {
        background: white;
        border-radius: 12px;
        padding: 30px;
        width: 600px;
        max-width: 90%;
        max-height: 80%;
        overflow-y: auto;
    }

    .anniversary-backgrounds-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 15px;
        margin-top: 20px;
    }

    .anniversary-bg-option {
        aspect-ratio: 1;
        border: 3px solid transparent;
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.2s;
        position: relative;
    }

    .anniversary-bg-option:hover {
        border-color: #0D7EE8;
        transform: scale(1.05);
    }

    .anniversary-bg-option.selected {
        border-color: #0D7EE8;
        box-shadow: 0 0 15px rgba(13,126,232,0.3);
    }

    .anniversary-bg-option img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* Background Type Selector */
    .background-type-selector {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
        justify-content: center;
    }

    .background-type-btn {
        padding: 12px 24px;
        border: 2px solid #ddd;
        background: white;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.2s;
        font-size: 14px;
    }

    .background-type-btn:hover {
        border-color: #0D7EE8;
        background: rgba(13,126,232,0.05);
    }

    .background-type-btn.active {
        border-color: #0D7EE8;
        background: #0D7EE8;
        color: white;
    }

    /* Background Sections */
    .background-section {
        margin-top: 20px;
    }

    /* Background Color Picker Container */
    .background-color-picker-container {
        display: flex;
        justify-content: center;
        padding: 20px;
    }

    .template-editor-actions {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }

    .template-editor-actions .modal-btn {
        padding: 12px 30px;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        min-width: 150px;
    }

    .template-editor-actions .modal-btn.apply {
        background: #007bff;
        color: white;
    }

    .template-editor-actions .modal-btn.apply:hover {
        background: #0056b3;
        transform: translateY(-1px);
    }

    .template-editor-actions .modal-btn.cancel {
        background: #6c757d;
        color: white;
    }

    .template-editor-actions .modal-btn.cancel:hover {
        background: #545b62;
        transform: translateY(-1px);
    }

    .template-editor-actions .modal-btn.secondary {
        background: #ffc107;
        color: #212529;
        border: 1px solid #ffc107;
    }

    .template-editor-actions .modal-btn.secondary:hover {
        background: #e0a800;
        border-color: #d39e00;
        transform: translateY(-1px);
    }

    /* Responsive adjustments for Anniversary */
    @media (max-width: 768px) {
        .anniversary-templates-container {
            grid-template-columns: 1fr;
        }

        .editor-controls {
            grid-template-columns: 1fr;
        }

        /* Template Editor Mobile Optimizations */
        .template-editor-container {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }

        .editor-canvas-container {
            margin-bottom: 20px;
        }

        .text-editing-panel,
        .image-editing-panel {
            padding: 15px;
            margin-bottom: 15px;
        }

        .text-editing-panel h3,
        .image-editing-panel h3 {
            font-size: 16px;
            margin-bottom: 15px;
        }

        .background-graphic-button {
            padding: 12px 20px;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .template-editor-actions {
            flex-direction: column;
            gap: 10px;
        }

        .modal-btn {
            padding: 12px 20px;
            font-size: 14px;
            width: 100%;
        }

        /* Background Graphic Dialog Mobile */
        .background-graphic-dialog .dialog-content {
            width: 95%;
            max-width: none;
            margin: 10px;
            padding: 20px;
        }

        .anniversary-backgrounds-grid {
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 10px;
        }

        .anniversary-bg-option {
            border-width: 2px;
        }
    }

    /* Extra small screens */
    @media (max-width: 480px) {
        .template-editor-container {
            padding: 10px;
            margin: 5px 0;
        }

        .text-editing-panel,
        .image-editing-panel {
            padding: 12px;
        }

        .anniversary-backgrounds-grid {
            grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
            gap: 8px;
        }

        .background-graphic-dialog .dialog-content {
            padding: 15px;
        }
    }
</style>

<!-- Purpose Display -->
    <div id="purposeDisplay" style="text-align: center; margin-bottom: 20px; padding: 15px; background: linear-gradient(135deg, #0D7EE8, #0A6AC7); color: white; border-radius: 8px; display: none;">
        <strong>Selected Purpose: <span id="selectedPurposeText"></span></strong>
    </div>

    <!-- Template Selection Section -->
    <div class="text-editor-section" id="templateSelectionSection">
        <h2 class="design-section-title" id="templateSectionTitle">Select a Design</h2>
        <div class="template-selection" id="templateSelection">
            <!-- Templates will be dynamically loaded based on purpose -->
            <div class="no-templates-message" id="noTemplatesMessage">
                <h3>Please Select a Purpose</h3>
                <p>Choose a purpose from the dropdown menu to view available templates.</p>
            </div>
        </div>
    </div>



    <!-- Interactive Anniversary Templates Section (shown only for Anniversary purpose) -->
    <div class="text-editor-section" id="anniversaryTemplatesSection" style="display: none;">
        <h2 class="design-section-title">Choose Your Anniversary Template</h2>
        <div class="anniversary-templates-container" id="anniversaryTemplatesContainer">
            <!-- Template 1: Full-size image layout -->
            <div class="anniversary-template-card" data-template="anniversary-template-1">
                <div class="template-preview">
                    <div class="template-layout horizontal-split">
                        <div class="text-section">
                            <div class="template-field-text">HAPPY</div>
                            <div class="template-field-text">ANNIVERSARY</div>
                            <div class="template-field-text">John & Jane</div>
                            <div class="template-field-text">Love: Family</div>
                        </div>
                        <div class="image-section full-image">
                            <img src="https://www.borgesmedia.com/wp-content/uploads/2025/06/default-image-person.jpg" alt="Default person">
                        </div>
                    </div>
                </div>
                <div class="template-label">Template 1: Full Image</div>
            </div>

            <!-- Template 2: Placeholder space layout -->
            <div class="anniversary-template-card" data-template="anniversary-template-2">
                <div class="template-preview">
                    <div class="template-layout horizontal-split">
                        <div class="text-section">
                            <div class="template-field-text">HAPPY</div>
                            <div class="template-field-text">ANNIVERSARY</div>
                            <div class="template-field-text">John & Jane</div>
                            <div class="template-field-text">Love: Family</div>
                        </div>
                        <div class="image-section placeholder-space">
                        </div>
                    </div>
                </div>
                <div class="template-label">Template 2: Upload Space</div>
            </div>

            <!-- Template 3: Centered smaller image layout -->
            <div class="anniversary-template-card" data-template="anniversary-template-3">
                <div class="template-preview">
                    <div class="template-layout horizontal-split">
                        <div class="text-section">
                            <div class="template-field-text">HAPPY</div>
                            <div class="template-field-text">ANNIVERSARY</div>
                            <div class="template-field-text">John & Jane</div>
                            <div class="template-field-text">Love: Family</div>
                        </div>
                        <div class="image-section centered-image">
                            <img src="https://www.borgesmedia.com/wp-content/uploads/2025/06/default-image-person.jpg" alt="Default person">
                        </div>
                    </div>
                </div>
                <div class="template-label">Template 3: Centered Image</div>
            </div>
        </div>
    </div>

    <!-- Interactive Benefit Templates Section (shown only for Benefit purpose) -->
    <div class="text-editor-section" id="benefitTemplatesSection" style="display: none;">
        <h2 class="design-section-title">Choose Your Benefit Template</h2>
        <div class="benefit-templates-container" id="benefitTemplatesContainer">
            <!-- Template 1: Classic Red Benefit -->
            <div class="benefit-template-card" data-template="benefit-template-1">
                <div class="template-preview">
                    <div class="template-layout horizontal-split">
                        <div class="text-section">
                            <div class="template-field-text">BENEFIT FOR</div>
                            <div class="template-field-text">John & Jane Smith</div>
                            <div class="template-field-text">for Medical Bills</div>
                            <div class="template-field-text">Jan. 12, 5 PM at the City Park</div>
                        </div>
                        <div class="image-section">
                            <img src="https://www.borgesmedia.com/wp-content/uploads/2025/06/default-image-person.jpg" alt="Default person">
                        </div>
                    </div>
                </div>
                <div class="template-label">Template 1: Classic Red</div>
            </div>

            <!-- Template 2: Text Only Black Background -->
            <div class="benefit-template-card" data-template="benefit-template-2">
                <div class="template-preview">
                    <div class="template-layout full-text">
                        <div class="text-section full-width">
                            <div class="template-field-text red-text large">BENEFIT FOR</div>
                            <div class="template-field-text white-text script">John & Jane Smith</div>
                            <div class="template-field-text white-text bold">for Medical Bills</div>
                            <div class="template-field-text red-text medium">Jan. 12, 5 PM at the City Park</div>
                        </div>
                    </div>
                </div>
                <div class="template-label">Template 2: Text Only</div>
            </div>

            <!-- Template 3: Green Accent Benefit -->
            <div class="benefit-template-card" data-template="benefit-template-3">
                <div class="template-preview">
                    <div class="template-layout horizontal-split">
                        <div class="text-section">
                            <div class="template-field-text">BENEFIT FOR</div>
                            <div class="template-field-text">John & Jane Smith</div>
                            <div class="template-field-text">for Medical Bills</div>
                            <div class="template-field-text">Jan. 12, 5 PM at the City Park</div>
                        </div>
                        <div class="image-section centered-image">
                            <img src="https://www.borgesmedia.com/wp-content/uploads/2025/06/default-image-person.jpg" alt="Default person">
                        </div>
                    </div>
                </div>
                <div class="template-label">Template 3: Green Accent</div>
            </div>
        </div>
    </div>

    <!-- UNIVERSAL TEMPLATE SECTIONS - Dynamic for any purpose -->
    <div class="text-editor-section" id="universalTemplatesSection" style="display: none;">
        <h2 class="design-section-title" id="universalTemplateTitle">Choose Your Template</h2>
        <div class="universal-templates-container" id="universalTemplatesContainer">
            <!-- Templates will be dynamically generated based on purpose -->
        </div>
    </div>

    <!-- Interactive Template Editor -->
    <div class="template-editor-container" id="templateEditorContainer">
        <h2 class="design-section-title">Customize Your Anniversary Billboard</h2>

        <!-- Canvas for editing -->
        <div class="editor-canvas-container">
            <canvas id="editorCanvas" class="editor-canvas" width="800" height="400"></canvas>
        </div>

        <!-- Editor Controls -->
        <div class="editor-controls">
            <!-- Text Editing Panel -->
            <div class="text-editing-panel">
                <h3>Edit Text</h3>
                <div class="text-line-container">
                    <input type="text" class="text-line-input" id="editorLine1" placeholder="Enter text for line 1" maxlength="50">
                    <div class="text-controls">
                        <div class="color-picker-dropdown">
                            <div class="color-picker-btn" id="editorColorBtn1" style="background-color: #ffffff;"></div>
                            <div class="color-dropdown" id="editorColorDropdown1">
                                <div class="color-palette" id="editorColorPalette1"></div>
                            </div>
                        </div>
                        <button type="button" class="settings-btn" id="editorSettingsBtn1" title="Font Settings">⚙️</button>
                    </div>
                </div>
                <div class="text-line-container">
                    <input type="text" class="text-line-input" id="editorLine2" placeholder="Enter text for line 2" maxlength="50">
                    <div class="text-controls">
                        <div class="color-picker-dropdown">
                            <div class="color-picker-btn" id="editorColorBtn2" style="background-color: #ffffff;"></div>
                            <div class="color-dropdown" id="editorColorDropdown2">
                                <div class="color-palette" id="editorColorPalette2"></div>
                            </div>
                        </div>
                        <button type="button" class="settings-btn" id="editorSettingsBtn2" title="Font Settings">⚙️</button>
                    </div>
                </div>
                <div class="text-line-container">
                    <input type="text" class="text-line-input" id="editorLine3" placeholder="Enter text for line 3" maxlength="50">
                    <div class="text-controls">
                        <div class="color-picker-dropdown">
                            <div class="color-picker-btn" id="editorColorBtn3" style="background-color: #ffffff;"></div>
                            <div class="color-dropdown" id="editorColorDropdown3">
                                <div class="color-palette" id="editorColorPalette3"></div>
                            </div>
                        </div>
                        <button type="button" class="settings-btn" id="editorSettingsBtn3" title="Font Settings">⚙️</button>
                    </div>
                </div>
                <div class="text-line-container">
                    <input type="text" class="text-line-input" id="editorLine4" placeholder="Enter text for line 4" maxlength="50">
                    <div class="text-controls">
                        <div class="color-picker-dropdown">
                            <div class="color-picker-btn" id="editorColorBtn4" style="background-color: #ffffff;"></div>
                            <div class="color-dropdown" id="editorColorDropdown4">
                                <div class="color-palette" id="editorColorPalette4"></div>
                            </div>
                        </div>
                        <button type="button" class="settings-btn" id="editorSettingsBtn4" title="Font Settings">⚙️</button>
                    </div>
                </div>
            </div>

            <!-- Image Editing Panel -->
            <div class="image-editing-panel">
                <h3>Customize Design</h3>
                <button type="button" class="background-graphic-button" id="backgroundGraphicBtn">
                    Background Graphic
                </button>
                <div class="upload-section">
                    <h4>Upload Your Photo</h4>
                    <div class="upload-container" id="editorPhotoUpload">
                        <div class="upload-icon">📷</div>
                        <div class="upload-text">Click to upload your photo</div>
                        <div class="upload-subtext">Accepted formats: JPG, PNG. High resolution recommended.</div>
                        <input type="file" id="editorPhotoInput" accept="image/jpeg,image/png" style="display: none;">
                    </div>
                    <div id="editorPhotoValidation" class="validation-message" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="template-editor-actions">
            <button type="button" class="modal-btn cancel" id="backToTemplatesBtn" onclick="window.backToTemplateSelection(true)" title="Reset all fields and go back to template selection">Back to Templates</button>
            <button type="button" class="modal-btn secondary" id="resetFieldsBtn" onclick="window.resetAllFields()" title="Clear all text fields and reset colors">Reset All Fields</button>
            <button type="button" class="modal-btn apply" id="confirmTemplateBtn" onclick="window.confirmDesignUniversal()">Confirm Design</button>
        </div>
    </div>

    <!-- Background Graphic Selection Dialog -->
    <div class="background-graphic-dialog" id="backgroundGraphicDialog">
        <div class="dialog-content">
            <h3>Select Anniversary Background</h3>

            <!-- Background Type Selector -->
            <div class="background-type-selector">
                <button type="button" class="background-type-btn active" id="imageBackgroundBtn" data-type="image">
                    🖼️ Image Background
                </button>
                <button type="button" class="background-type-btn" id="colorBackgroundBtn" data-type="color">
                    🎨 Color Background
                </button>
            </div>

            <!-- Image Background Section -->
            <div class="background-section" id="imageBackgroundSection">
                <div class="anniversary-backgrounds-grid" id="anniversaryBackgroundsGrid">
                    <!-- Anniversary backgrounds will be populated here -->
                </div>
            </div>

            <!-- Color Background Section -->
            <div class="background-section" id="colorBackgroundSection" style="display: none;">
                <div class="background-color-picker-container">
                    <div class="color-picker-advanced">
                        <div class="color-gradient-area" id="backgroundColorGradient">
                            <div class="color-gradient-picker" id="backgroundColorPicker"></div>
                        </div>
                        <div class="hue-slider-container">
                            <div class="hue-slider" id="backgroundHueSlider">
                                <div class="hue-slider-handle" id="backgroundHueHandle"></div>
                            </div>
                        </div>
                        <div class="color-preview-section">
                            <div class="color-preview-large" id="backgroundColorPreview" style="background-color: #ff0000;"></div>
                            <div class="color-picker-buttons">
                                <button type="button" class="color-picker-btn-small" id="backgroundColorReset">Reset</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-buttons">
                <button type="button" class="modal-btn apply" id="selectBackgroundBtn">Select</button>
                <button type="button" class="modal-btn cancel" id="cancelBackgroundBtn">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Editor Font Settings Modal -->
    <div class="font-modal" id="editorFontModal" style="display: none;">
        <div class="font-modal-content">
            <h3>Font Settings</h3>

            <div class="font-setting-row">
                <div class="font-setting-label">Font Family</div>
                <div class="font-setting-control">
                    <select id="editorModalFontSelect" class="font-family-dropdown">
                        <option value="Mouse Memoirs" style="font-family: 'Mouse Memoirs';">Mouse Memoirs</option>
                        <option value="Domine" style="font-family: Domine;">Domine</option>
                        <option value="Baloo Tamma" style="font-family: 'Baloo Tamma';">Baloo Tamma</option>
                        <option value="Courgette" style="font-family: Courgette;">Courgette</option>
                        <option value="Oswald" style="font-family: Oswald;">Oswald</option>
                        <option value="Kaushan Script" style="font-family: 'Kaushan Script';">Kaushan Script</option>
                        <option value="Alfa Slab One" style="font-family: 'Alfa Slab One';">Alfa Slab One</option>
                        <option value="Montserrat" style="font-family: Montserrat;">Montserrat</option>
                        <option value="Yellowtail" style="font-family: Yellowtail;">Yellowtail</option>
                        <option value="Paytone One" style="font-family: 'Paytone One';">Paytone One</option>
                        <option value="Indie Flower" style="font-family: 'Indie Flower';">Indie Flower</option>
                        <option value="Dancing Script" style="font-family: 'Dancing Script';">Dancing Script</option>
                        <option value="Anton" style="font-family: Anton;">Anton</option>
                        <option value="Luckiest Guy" style="font-family: 'Luckiest Guy';">Luckiest Guy</option>
                        <option value="Permanent Marker" style="font-family: 'Permanent Marker';">Permanent Marker</option>
                        <option value="Sniglet" style="font-family: Sniglet;">Sniglet</option>
                        <option value="Lobster" style="font-family: Lobster;">Lobster</option>
                        <option value="Arial" style="font-family: Arial;">Arial</option>
                    </select>
                </div>
            </div>

            <div class="font-setting-row">
                <div class="font-setting-label">Font Size</div>
                <div class="font-setting-control">
                    <select id="editorModalFontSize" class="font-size-dropdown">
                        <!-- Options will be populated by JavaScript -->
                    </select>
                </div>
            </div>

            <div class="font-setting-row">
                <div class="font-setting-label">Shadow?</div>
                <div class="font-setting-control">
                    <input type="checkbox" id="editorModalShadow" class="shadow-checkbox" checked>
                </div>
            </div>

            <div class="font-setting-row">
                <div class="font-setting-label">Shadow Width</div>
                <div class="font-setting-control">
                    <input type="range" id="editorModalShadowWidth" class="shadow-width-slider" min="0" max="10" value="4">
                </div>
            </div>

            <div class="apply-all-checkbox">
                <input type="checkbox" id="editorApplyToAll">
                <label for="editorApplyToAll">Apply to all text areas</label>
            </div>

            <div class="modal-buttons">
                <button type="button" class="modal-btn apply" onclick="applyEditorFontSettings()">Apply</button>
                <button type="button" class="modal-btn cancel" onclick="closeEditorFontModal()">Cancel</button>
            </div>
        </div>
    </div>
    
    <!-- Font Settings Modal -->
    <div class="font-modal" id="fontModal">
        <div class="font-modal-content">
            <div class="font-setting-row">
                <div class="font-setting-label">Font</div>
                <div class="font-setting-control">
                    <select id="modalFontSelect" class="font-dropdown">
                        <option value="Mouse Memoirs" style="font-family: 'Mouse Memoirs';">Mouse Memoirs</option>
                        <option value="Domine" style="font-family: Domine;">Domine</option>
                        <option value="Baloo Tamma" style="font-family: 'Baloo Tamma';">Baloo Tamma</option>
                        <option value="Courgette" style="font-family: Courgette;">Courgette</option>
                        <option value="Oswald" style="font-family: Oswald;">Oswald</option>
                        <option value="Kaushan Script" style="font-family: 'Kaushan Script';">Kaushan Script</option>
                        <option value="Alfa Slab One" style="font-family: 'Alfa Slab One';">Alfa Slab One</option>
                        <option value="Montserrat" style="font-family: Montserrat;">Montserrat</option>
                        <option value="Yellowtail" style="font-family: Yellowtail;">Yellowtail</option>
                        <option value="Paytone One" style="font-family: 'Paytone One';">Paytone One</option>
                        <option value="Indie Flower" style="font-family: 'Indie Flower';">Indie Flower</option>
                        <option value="Dancing Script" style="font-family: 'Dancing Script';">Dancing Script</option>
                        <option value="Anton" style="font-family: Anton;">Anton</option>
                        <option value="Luckiest Guy" style="font-family: 'Luckiest Guy';">Luckiest Guy</option>
                        <option value="Permanent Marker" style="font-family: 'Permanent Marker';">Permanent Marker</option>
                        <option value="Sniglet" style="font-family: Sniglet;">Sniglet</option>
                        <option value="Lobster" style="font-family: Lobster;">Lobster</option>
                        <option value="Arial" style="font-family: Arial;">Arial</option>
                    </select>
                </div>
            </div>

            <div class="font-setting-row">
                <div class="font-setting-label">Font Size</div>
                <div class="font-setting-control">
                    <select id="modalFontSize" class="font-size-dropdown">
                        <!-- Options will be populated by JavaScript -->
                    </select>
                </div>
            </div>

            <div class="font-setting-row">
                <div class="font-setting-label">Shadow?</div>
                <div class="font-setting-control">
                    <input type="checkbox" id="modalShadow" class="shadow-checkbox" checked>
                </div>
            </div>

            <div class="font-setting-row">
                <div class="font-setting-label">Shadow Width</div>
                <div class="font-setting-control">
                    <input type="range" id="modalShadowWidth" class="shadow-width-slider" min="0" max="10" value="4">
                </div>
            </div>

            <div class="apply-all-checkbox">
                <input type="checkbox" id="applyToAll">
                <label for="applyToAll">Apply to all text areas</label>
            </div>

            <div class="modal-buttons">
                <button type="button" class="modal-btn apply" onclick="applyFontSettings()">Apply</button>
                <button type="button" class="modal-btn cancel" onclick="closeFontModal()">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Hidden fields to store the form data -->
    <input type="hidden" id="selectedTemplate" name="selectedTemplate" value="template1">
    <input type="hidden" id="selectedBackground" name="selectedBackground" value="none">
    <input type="hidden" id="selectedPurpose" name="selectedPurpose" value="">
    <input type="hidden" id="textSettings" name="textSettings" value="">
    <input type="hidden" id="billboardData" name="billboardData" value="">

    <!-- Submit Button with Professional Status Indicator -->
    <div class="submit-container">
        <button type="button" class="submit-button" id="checkoutButton" onclick="proceedToCheckout()">
            <span id="buttonText">Continue to Checkout</span>
            <span id="buttonStatus" style="font-size: 12px; opacity: 0.8; display: block; margin-top: 2px;"></span>
        </button>
    </div>

    <script>
    // Utility function to populate font size dropdowns
    function populateFontSizeDropdown(selectId, defaultValue = 50) {
        const select = document.getElementById(selectId);
        if (!select) return;

        // Clear existing options
        select.innerHTML = '';

        // Generate font size options from 10 to 150 in increments of 5
        for (let size = 10; size <= 150; size += 5) {
            const option = document.createElement('option');
            option.value = size;
            option.textContent = size;
            if (size === defaultValue) {
                option.selected = true;
            }
            select.appendChild(option);
        }
    }

    // Initialize font size dropdowns when page loads
    document.addEventListener('DOMContentLoaded', function() {
        populateFontSizeDropdown('modalFontSize', 50);
        populateFontSizeDropdown('editorModalFontSize', 50);
    });

    // Professional button status updater
    function updateCheckoutButtonStatus() {
        const button = document.getElementById('checkoutButton');
        const statusSpan = document.getElementById('buttonStatus');

        if (!button || !statusSpan) return;

        const requiredData = {
            purpose: localStorage.getItem('selectedPurpose'),
            location: localStorage.getItem('billboardLocation'),
            dates: localStorage.getItem('runDates')
        };

        const missingFields = [];
        Object.keys(requiredData).forEach(field => {
            const value = requiredData[field];
            if (!value || value === 'Select a Purpose' || value === 'Not selected' || value.trim() === '') {
                missingFields.push(field);
            }
        });

        if (missingFields.length === 0) {
            statusSpan.textContent = '✅ Ready for checkout';
            statusSpan.style.color = '#28a745';
            button.style.backgroundColor = '#28a745';
            button.style.opacity = '1';
        } else {
            statusSpan.textContent = `⚠️ Missing: ${missingFields.join(', ')}`;
            statusSpan.style.color = '#ffc107';
            button.style.backgroundColor = '#6c757d';
            button.style.opacity = '0.7';
        }
    }

    // Update button status periodically
    setInterval(updateCheckoutButtonStatus, 2000);

    // Update immediately on page load
    setTimeout(updateCheckoutButtonStatus, 1000);

    // Professional auto-save functionality
    function setupAutoSave() {
        // Auto-save when text fields change
        ['line1', 'line2', 'line3', 'line4'].forEach(lineId => {
            const element = document.getElementById(lineId);
            if (element) {
                element.addEventListener('input', function() {
                    console.log(`Auto-save triggered for ${lineId}`);
                    setTimeout(saveBillboardData, 500); // Debounced save
                });
            }
        });

        // Auto-save when editor text fields change
        ['editorLine1', 'editorLine2', 'editorLine3', 'editorLine4'].forEach(lineId => {
            const element = document.getElementById(lineId);
            if (element) {
                element.addEventListener('input', function() {
                    console.log(`Auto-save triggered for ${lineId}`);
                    setTimeout(saveBillboardData, 500); // Debounced save
                });
            }
        });

        console.log('Auto-save functionality initialized');
    }

    // Initialize auto-save after page loads
    setTimeout(setupAutoSave, 2000);
    </script>

<!-- Image Resize Modal -->
<div id="imageResizeModal" class="image-resize-modal">
    <div class="image-resize-content">
        <div class="image-resize-container">
            <div class="image-resize-canvas" id="imageResizeCanvas">
                <img id="resizeImage" class="resize-image" src="" alt="Image to resize">
                <div id="cropOverlay" class="crop-overlay">
                    <div class="crop-handle nw"></div>
                    <div class="crop-handle ne"></div>
                    <div class="crop-handle sw"></div>
                    <div class="crop-handle se"></div>
                </div>
            </div>
        </div>
        <div class="image-resize-buttons">
            <button class="resize-btn cancel" id="cancelResizeBtn">Cancel</button>
            <button class="resize-btn done" id="confirmResizeBtn">Done</button>
        </div>
    </div>
</div>

<script>
    // Load Google Fonts
    const link = document.createElement('link');
    link.href = 'https://fonts.googleapis.com/css2?family=Mouse+Memoirs&family=Domine:wght@400;700&family=Baloo+Tamma+2:wght@400;700&family=Courgette&family=Oswald:wght@300;400;700&family=Kaushan+Script&family=Alfa+Slab+One&family=Montserrat:wght@400;700;900&family=Yellowtail&family=Paytone+One&family=Indie+Flower&family=Dancing+Script:wght@400;700&family=Anton&family=Luckiest+Guy&family=Permanent+Marker&family=Sniglet:wght@400;800&family=Lobster&display=swap';
    link.rel = 'stylesheet';
    document.head.appendChild(link);

    document.addEventListener('DOMContentLoaded', function() {
        // Global variables
        let currentPurpose = null;
        let currentTemplate = 'anniversary1';
        let currentFont = 'Arial';
        let currentBgColor = '#ff6b6b';
        let currentBgGraphic = 'none';
        let uploadedImage = null;

        // Canvas and context (removed billboard canvas, keeping editor canvas)
        let canvas = null;
        let ctx = null;

        // Text settings for each line
        const textSettings = {
            line1: { color: '#ffffff', fontSize: 50, fontFamily: 'Arial', shadow: true, shadowWidth: 4 },
            line2: { color: '#ffffff', fontSize: 50, fontFamily: 'Arial', shadow: true, shadowWidth: 4 },
            line3: { color: '#ffffff', fontSize: 50, fontFamily: 'Arial', shadow: true, shadowWidth: 4 },
            line4: { color: '#ffffff', fontSize: 50, fontFamily: 'Arial', shadow: true, shadowWidth: 4 }
        };

        // Anniversary Template definitions
        const anniversaryTemplates = {
            'anniversary-template-1': {
                type: 'full-image',
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-1.png',
                textPositions: [
                    { x: 200, y: 120, align: 'center' },
                    { x: 200, y: 180, align: 'center' },
                    { x: 200, y: 260, align: 'center' },
                    { x: 200, y: 320, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }, // BENEFIT FOR - white bold with shadow
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }, // Names - white bold with shadow
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }, // Purpose - white bold with shadow
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }  // Date/location - white bold with shadow
                ],
                imagePosition: { x: 400, y: 0, width: 400, height: 400 },
                defaultImage: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/default-image-person.jpg',
                defaultTexts: ['HAPPY', 'ANNIVERSARY', 'John & Jane Smith', 'Love: Family']
            },
            'anniversary-template-2': {
                type: 'placeholder-space',
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-2.png',
                textPositions: [
                    { x: 200, y: 120, align: 'center' },
                    { x: 200, y: 180, align: 'center' },
                    { x: 200, y: 260, align: 'center' },
                    { x: 200, y: 320, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }, // BENEFIT FOR - white bold with shadow
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }, // Names - white bold with shadow
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }, // Purpose - white bold with shadow
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }  // Date/location - white bold with shadow
                ],
                imagePosition: { x: 400, y: 0, width: 400, height: 400 },
                //placeholderText: 'Upload Your Photo'
                defaultTexts: ['HAPPY', 'ANNIVERSARY', 'John & Jane Smith', 'Love: Family']
            },
            'anniversary-template-3': {
                type: 'centered-image',
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-3.png',
                textPositions: [
                    { x: 200, y: 120, align: 'center' },
                    { x: 200, y: 180, align: 'center' },
                    { x: 200, y: 260, align: 'center' },
                    { x: 200, y: 320, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }, // BENEFIT FOR - white bold with shadow
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }, // Names - white bold with shadow
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }, // Purpose - white bold with shadow
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }  // Date/location - white bold with shadow
                ],
                imagePosition: { x: 500, y: 100, width: 200, height: 200 },
                defaultImage: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/default-image-person.jpg',
                defaultTexts: ['HAPPY', 'ANNIVERSARY', 'John & Jane Smith', 'Love: Family']
            }
        };

        // Benefit Template definitions
        const benefitTemplates = {
            'benefit-template-1': {
                type: 'full-image',
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-3.png',
                textPositions: [
                    { x: 200, y: 120, align: 'center' },
                    { x: 200, y: 180, align: 'center' },
                    { x: 200, y: 260, align: 'center' },
                    { x: 200, y: 320, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '22px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }, // BENEFIT FOR - white bold with shadow
                    { color: '#ffffff', fontSize: '22px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }, // Names - white bold with shadow
                    { color: '#ffffff', fontSize: '22px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }, // Purpose - white bold with shadow
                    { color: '#ffffff', fontSize: '22px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }  // Date/location - white bold with shadow
                ],
                imagePosition: { x: 500, y: 55, width: 300, height: 300 },
                defaultImage: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/default-image-person.jpg',
                defaultTexts: ['BENEFIT FOR', 'John & Jane Smith', 'for Medical Bills', 'Jan. 12, 5 PM at the City Park']
            },
            'benefit-template-2': {
                type: 'text-only',
                background: '#000000', // Black background
                textPositions: [
                    { x: 400, y: 80, align: 'center' },  // BENEFIT FOR - top center
                    { x: 400, y: 180, align: 'center' }, // John & Jane Smith - center
                    { x: 400, y: 260, align: 'center' }, // for Medical Bills - center
                    { x: 400, y: 360, align: 'center' }  // Date/location - bottom center
                ],
                textStyles: [
                    { color: '#ff0000', fontSize: '72px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '4px' }, // BENEFIT FOR - large red
                    { color: '#ffffff', fontSize: '48px', fontFamily: 'Dancing Script', fontStyle: 'italic' },           // Names - white script
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: '700' },                      // Purpose - white bold
                    { color: '#ff0000', fontSize: '36px', fontFamily: 'Arial', fontWeight: '700' }                       // Date/location - red
                ],
                imagePosition: null, // No image for this template
                defaultImage: null,
                defaultTexts: ['BENEFIT FOR', 'John & Jane Smith', 'for Medical Bills', 'Jan. 12, 5 PM at the City Park']
            },
            'benefit-template-3': {
                type: 'centered-image',
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-5.png',
                textPositions: [
                    { x: 200, y: 120, align: 'center' },
                    { x: 200, y: 180, align: 'center' },
                    { x: 200, y: 260, align: 'center' },
                    { x: 200, y: 320, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '22px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }, // BENEFIT FOR - white bold with shadow
                    { color: '#ffffff', fontSize: '22px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }, // Names - white bold with shadow
                    { color: '#ffffff', fontSize: '22px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }, // Purpose - white bold with shadow
                    { color: '#ffffff', fontSize: '22px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }  // Date/location - white bold with shadow
                ],
                imagePosition: { x: 500, y: 100, width: 200, height: 200 },
                defaultImage: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/default-image-person.jpg',
                defaultTexts: ['BENEFIT FOR', 'John & Jane Smith', 'for Medical Bills', 'Jan. 12, 5 PM at the City Park']
            }
        };

        // Template definitions (keeping existing for compatibility)
        const templates = {
            template1: {
                background: 'linear-gradient(45deg, #ff6b6b, #feca57)',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            template2: {
                background: 'linear-gradient(45deg, #48dbfb, #0abde3)',
                textPositions: [
                    { x: 400, y: 100, align: 'center' },
                    { x: 400, y: 160, align: 'center' },
                    { x: 400, y: 240, align: 'center' },
                    { x: 400, y: 300, align: 'center' }
                ]
            },
            template3: {
                background: 'linear-gradient(45deg, #ff9ff3, #f368e0)',
                textPositions: [
                    { x: 400, y: 110, align: 'center' },
                    { x: 400, y: 170, align: 'center' },
                    { x: 400, y: 250, align: 'center' },
                    { x: 400, y: 310, align: 'center' }
                ]
            },
            // Anniversary templates
            anniversary1: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-1.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            anniversary2: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-2.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            anniversary3: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-3.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            anniversary4: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-6.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            anniversary5: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-8.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            anniversary6: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-9.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            anniversary7: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-11.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            anniversary8: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-12.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            anniversary9: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-13.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            anniversary10: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-14.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            anniversary11: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-15.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            anniversary12: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-4.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            anniversary13: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-5.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            anniversary14: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-10.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            anniversary15: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-7.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            // Benefit templates
            benefit1: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-1.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            benefit2: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-2.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            benefit3: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-3.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            benefit4: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-4.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            benefit5: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-5.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            benefit6: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-6.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            benefit7: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-7.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            benefit8: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-8.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            benefit9: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-9.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            benefit10: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-10.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            benefit11: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-11.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            benefit12: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-12.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            benefit13: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-13.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            benefit14: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-14.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            benefit15: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-15.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            // Christian templates
            christian1: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-1.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            christian2: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-2.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            christian3: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-3.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            christian4: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-4.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            christian5: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-5.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            christian6: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-6.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            christian7: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-7.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            christian8: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-8.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            christian9: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-9.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            christian10: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-10.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            christian11: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-11.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            christian12: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-12_.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            christian13: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-13.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            christian14: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-14.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            christian15: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-15.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            // Graduation templates
            graduation1: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-1.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            graduation2: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-2.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            graduation3: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-3.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            graduation4: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-4.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            graduation5: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-5.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            graduation6: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-6.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            graduation7: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-7.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            graduation8: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-8.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            graduation9: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-9.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            graduation10: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-10.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            graduation11: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-11.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            graduation12: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-12.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            graduation13: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-13.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            graduation14: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-14.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            graduation15: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-15.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            // Holiday templates
            holiday1: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/4th-of-July-1.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            holiday2: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/4th-of-July-2.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            holiday3: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/4th-of-July-3.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            holiday4: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/4th-of-July-4.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            holiday5: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/4th-of-July-5.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            holiday6: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christmas-1.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            holiday7: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christmas-2.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            holiday8: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christmas-3.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            holiday9: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christmas-4.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            holiday9a: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christmas-5.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            holiday10: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Happy-New-Year-1.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            holiday11: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Happy-New-Year-2.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            holiday12: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Happy-New-Year-3.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            holiday13: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Happy-New-Year-4.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            holiday14: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Happy-New-Year-5.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            // Local School templates
            localschool1: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Bluff-Dale-ISD.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            localschool2: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Dublin-ISD_Green_Background.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            localschool3: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Dublin-ISD_Yellow_Background.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            localschool4: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Huckabay-ISD_Background.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            localschool5: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Lingleville-ISD.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            localschool6: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Lingleville-ISD.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            localschool7: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Morgan-Mill-ISD.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            localschool8: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Stephenville-ISD-Bees.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            localschool9: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Stephenville-ISD_Jackets-2.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            localschool10: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Stephenville-ISD_Jackets.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            localschool11: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Three-Way-ISD_Background.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            // Love templates
            love1: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-1.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            love2: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-2.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            love3: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-3.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            love4: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-4.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            love5: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-5.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            love6: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-6.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            love7: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-7.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            love8: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-8.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            love9: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-9.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            love10: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-10.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            love11: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-11.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            love12: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-12.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            love13: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-13.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            love14: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-14.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            },
            love15: {
                background: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-15.png',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            }
        };

        // Debug function to check elements
        function debugElements() {
            const elements = [
                'billboardCanvas', 'line1', 'line2', 'line3', 'line4',
                'colorBtn1', 'colorBtn2', 'colorBtn3', 'colorBtn4',
                'settingsBtn1', 'settingsBtn2', 'settingsBtn3', 'settingsBtn4',
                'fontModal', 'modalFontSelect', 'modalFontSize'
            ];

            elements.forEach(id => {
                const element = document.getElementById(id);
                if (!element) {
                    console.warn(`Element not found: ${id}`);
                } else {
                    console.log(`Element found: ${id}`);
                }
            });
        }

        // Initialize
        function init() {
            console.log('Initializing billboard editor...');
            debugElements();
            initializePurpose();
            setupEventListeners();

            // Note: Billboard canvas removed, using anniversary templates instead
        }

        // Check for purpose from URL parameters or localStorage
        function initializePurpose() {
            const urlParams = new URLSearchParams(window.location.search);
            const purposeFromUrl = urlParams.get('purpose');
            const purposeFromStorage = localStorage.getItem('selectedPurpose');

            if (purposeFromUrl) {
                currentPurpose = purposeFromUrl;
                localStorage.setItem('selectedPurpose', purposeFromUrl);
            } else if (purposeFromStorage) {
                currentPurpose = purposeFromStorage;
            }

            if (currentPurpose) {
                displayPurpose(currentPurpose);
                updatePurposeField(currentPurpose);
            } else {
                // No purpose selected, show default state
                showDefaultTemplateState();
            }
        }

        // Show default template state when no purpose is selected
        function showDefaultTemplateState() {
            const templateSelectionSection = document.getElementById('templateSelectionSection');
            const anniversaryTemplatesSection = document.getElementById('anniversaryTemplatesSection');
            const noTemplatesMessage = document.getElementById('noTemplatesMessage');
            const purposeDisplay = document.getElementById('purposeDisplay');

            // Hide purpose display
            if (purposeDisplay) {
                purposeDisplay.style.display = 'none';
            }

            // Show template selection section with no templates message
            templateSelectionSection.style.display = 'block';
            anniversaryTemplatesSection.style.display = 'none';

            // Show no templates message
            if (noTemplatesMessage) {
                noTemplatesMessage.style.display = 'block';
            }
        }

        // Display the selected purpose
        function displayPurpose(purpose) {
            const purposeDisplay = document.getElementById('purposeDisplay');
            const selectedPurposeText = document.getElementById('selectedPurposeText');
            selectedPurposeText.textContent = purpose;
            purposeDisplay.style.display = 'block';

            // Update template section title
            const templateSectionTitle = document.getElementById('templateSectionTitle');
            if (templateSectionTitle) {
                templateSectionTitle.textContent = `Select Your ${purpose} Template`;
            }

            // Show appropriate template section based on purpose
            showTemplatesForPurpose(purpose);
        }

        // Show templates based on purpose - Enhanced for Universal Template System
        function showTemplatesForPurpose(purpose) {
            const templateSelectionSection = document.getElementById('templateSelectionSection');
            const anniversaryTemplatesSection = document.getElementById('anniversaryTemplatesSection');
            const universalTemplatesSection = document.getElementById('universalTemplatesSection');
            const noTemplatesMessage = document.getElementById('noTemplatesMessage');
            const editorContainer = document.getElementById('templateEditorContainer');

            // Hide editor if it's currently showing
            if (editorContainer && editorContainer.classList.contains('active')) {
                editorContainer.classList.remove('active');
                isEditorMode = false;
            }

            // Reset any selected templates
            document.querySelectorAll('.anniversary-template-card, .benefit-template-card, .universal-template-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Hide all template sections first
            templateSelectionSection.style.display = 'none';
            anniversaryTemplatesSection.style.display = 'none';
            const benefitTemplatesSection = document.getElementById('benefitTemplatesSection');
            if (benefitTemplatesSection) {
                benefitTemplatesSection.style.display = 'none';
            }
            if (universalTemplatesSection) {
                universalTemplatesSection.style.display = 'none';
            }

            if (purpose === 'Anniversary') {
                // Show anniversary interactive templates
                anniversaryTemplatesSection.style.display = 'block';

                // Update the anniversary section title
                const anniversaryTitle = anniversaryTemplatesSection.querySelector('.design-section-title');
                if (anniversaryTitle) {
                    anniversaryTitle.textContent = 'Choose Your Anniversary Template';
                }
            } else if (purpose === 'Benefit') {
                // Show benefit interactive templates
                if (benefitTemplatesSection) {
                    benefitTemplatesSection.style.display = 'block';

                    // Update the benefit section title
                    const benefitTitle = benefitTemplatesSection.querySelector('.design-section-title');
                    if (benefitTitle) {
                        benefitTitle.textContent = 'Choose Your Benefit Template';
                    }
                }
            } else if (UNIVERSAL_TEMPLATE_CONFIG[purpose]) {
                // Show universal template system for configured purposes
                if (universalTemplatesSection) {
                    universalTemplatesSection.style.display = 'block';

                    // Update the universal section title
                    const universalTitle = document.getElementById('universalTemplateTitle');
                    if (universalTitle) {
                        universalTitle.textContent = `Choose Your ${purpose} Template`;
                    }

                    // Load universal templates for the selected purpose
                    loadUniversalTemplatesForPurpose(purpose);
                }
            } else {
                // Show regular template selection for other purposes
                templateSelectionSection.style.display = 'block';

                // Update the template section title
                const templateSectionTitle = document.getElementById('templateSectionTitle');
                if (templateSectionTitle) {
                    templateSectionTitle.textContent = `Select Your ${purpose} Template`;
                }

                // Load templates for the selected purpose
                loadTemplatesForPurpose(purpose);
            }
        }

        // Update purpose in hidden field
        function updatePurposeField(purpose) {
            const selectedPurposeInput = document.getElementById('selectedPurpose');
            if (selectedPurposeInput) {
                selectedPurposeInput.value = purpose;
            }
        }

        // Load Universal Templates for a specific purpose
        function loadUniversalTemplatesForPurpose(purpose) {
            const universalContainer = document.getElementById('universalTemplatesContainer');
            if (!universalContainer) return;

            const config = UNIVERSAL_TEMPLATE_CONFIG[purpose];
            if (!config || !config.templates) {
                universalContainer.innerHTML = `
                    <div class="no-templates-message">
                        <h3>No templates available for ${purpose}</h3>
                        <p>Templates for this purpose are coming soon.</p>
                    </div>
                `;
                return;
            }

            // Clear existing templates
            universalContainer.innerHTML = '';

            // Generate template cards
            config.templates.forEach(template => {
                const templateCard = document.createElement('div');
                templateCard.className = 'universal-template-card';
                templateCard.dataset.template = template.id;
                templateCard.dataset.purpose = purpose;

                // Create preview content based on template configuration
                let previewContent = '';
                let backgroundStyle = '';

                if (template.backgroundImage) {
                    // Use background image (for Anniversary templates)
                    backgroundStyle = `background-image: url('${template.backgroundImage}'); background-size: cover; background-position: center;`;
                } else if (template.backgroundColor) {
                    // Use solid background color
                    backgroundStyle = `background-color: ${template.backgroundColor};`;
                } else {
                    // Default background
                    backgroundStyle = 'background-color: #000000;';
                }

                previewContent = `
                    <div class="universal-template-content" style="${backgroundStyle}">
                        ${config.defaultTexts.map((text, index) => {
                            const style = template.textStyles && template.textStyles[index] ? template.textStyles[index] : {};
                            return `<div class="universal-template-text" style="
                                color: ${style.color || '#ffffff'};
                                font-size: ${parseInt(style.fontSize) / 3 || 16}px;
                                font-family: ${style.fontFamily || 'Arial'};
                                font-weight: ${style.fontWeight || 'normal'};
                                font-style: ${style.fontStyle || 'normal'};
                                letter-spacing: ${style.letterSpacing || 'normal'};
                            ">${text}</div>`;
                        }).join('')}
                    </div>
                `;

                templateCard.innerHTML = `
                    <div class="universal-template-preview">
                        ${previewContent}
                    </div>
                    <div class="universal-template-label">${template.name}</div>
                `;

                // Add click event listener
                templateCard.addEventListener('click', function() {
                    selectUniversalTemplate(template.id, purpose);
                });

                universalContainer.appendChild(templateCard);
            });
        }

        // Select Universal Template
        function selectUniversalTemplate(templateId, purpose) {
            // Remove previous selection
            document.querySelectorAll('.universal-template-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Select current template
            const selectedCard = document.querySelector(`[data-template="${templateId}"]`);
            if (selectedCard) {
                selectedCard.classList.add('selected');
            }

            // Store current template and purpose
            window.editorState = window.editorState || {};
            window.editorState.currentTemplate = templateId;
            window.editorState.currentPurpose = purpose;

            // Get template configuration
            const config = UNIVERSAL_TEMPLATE_CONFIG[purpose];
            const template = config.templates.find(t => t.id === templateId);

            if (template) {
                // Update editor with template data
                updateEditorWithTemplate(template, config);

                // Show editor
                showUniversalTemplateEditor(purpose);
            }
        }

        // Update Editor with Template Data
        function updateEditorWithTemplate(template, config) {
            // Update text inputs with default texts and placeholders
            config.defaultTexts.forEach((text, index) => {
                const input = document.getElementById(`editorLine${index + 1}`);
                if (input) {
                    input.value = text;
                    input.placeholder = text;
                }
            });

            // Update any remaining inputs with generic placeholders
            for (let i = config.defaultTexts.length; i < 4; i++) {
                const input = document.getElementById(`editorLine${i + 1}`);
                if (input) {
                    input.value = '';
                    input.placeholder = `Text Line ${i + 1}`;
                }
            }

            // Store template configuration for canvas rendering
            window.editorState.templateConfig = template;
            window.editorState.purposeConfig = config;
        }

        // Show Universal Template Editor
        function showUniversalTemplateEditor(purpose) {
            const universalSection = document.getElementById('universalTemplatesSection');
            const editorContainer = document.getElementById('templateEditorContainer');

            if (universalSection && editorContainer) {
                universalSection.style.display = 'none';
                editorContainer.classList.add('active');
                isEditorMode = true;

                // Update editor title dynamically
                updateEditorTitleForPurpose(purpose);

                // Add event listeners for text inputs to update canvas
                setupUniversalTextInputListeners();

                // Initialize editor canvas
                setTimeout(() => {
                    updateUniversalEditorCanvas();
                }, 100);
            }
        }

        // Update Editor Title for any Purpose
        function updateEditorTitleForPurpose(purpose) {
            const editorTitle = document.getElementById('editorTitle');
            if (editorTitle) {
                editorTitle.textContent = `Customize Your ${purpose} Billboard`;
            }
        }

        // Setup event listeners for universal template text inputs
        function setupUniversalTextInputListeners() {
            const textInputs = ['editorLine1', 'editorLine2', 'editorLine3', 'editorLine4'];

            textInputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                if (input) {
                    // Remove existing listeners to avoid duplicates
                    input.removeEventListener('input', updateUniversalEditorCanvas);
                    input.removeEventListener('keyup', updateUniversalEditorCanvas);

                    // Add new listeners
                    input.addEventListener('input', updateUniversalEditorCanvas);
                    input.addEventListener('keyup', updateUniversalEditorCanvas);
                }
            });
        }

        // Unified color palette - clean and organized
        const colorPalette = [
            // Row 1: Grays and whites
            '#000000', '#333333', '#555555', '#777777', '#999999', '#bbbbbb', '#dddddd', '#eeeeee', '#f5f5f5', '#ffffff',

            // Row 2: Primary colors
            '#ff0000', '#ff6600', '#ffff00', '#00ff00', '#00ffff', '#0066ff', '#0000ff', '#6600ff', '#ff00ff', '#ff0066',

            // Row 3: Light tints
            '#ffcccc', '#ffddcc', '#ffffcc', '#ccffcc', '#ccffff', '#ccddff', '#ccccff', '#ddccff', '#ffccff', '#ffccdd',

            // Row 4: Medium tones
            '#ff9999', '#ffbb99', '#ffff99', '#99ff99', '#99ffff', '#99bbff', '#9999ff', '#bb99ff', '#ff99ff', '#ff99bb',

            // Row 5: Vibrant colors
            '#ff6600', '#99ff00', '#00ff99', '#0099ff', '#9900ff', '#ff0099', '#cc3300', '#ff3300', '#ff6600', '#cc6600',

            // Row 6: Dark tones
            '#663300', '#336600', '#003366', '#330066', '#660033', '#330000', '#003300', '#000033', '#660066', '#663333',

            // Row 7: Special colors with checkmark
            '✓', '#ffff66', '#ffff99', '#ffffcc', '#cccccc', '#000000'
        ];

        let currentEditingLine = null;

        // Universal Template Configuration - Flexible for any purpose
        const UNIVERSAL_TEMPLATE_CONFIG = {
            'Graduation': {
                defaultTexts: ['CONGRATULATIONS', 'Class of 2024', 'Achievement Unlocked', 'Proud Graduate'],
                templates: [
                    {
                        id: 'graduation-template-1',
                        name: 'Classic Graduation',
                        backgroundColor: '#000080',
                        textStyles: [
                            { color: '#FFD700', fontSize: '44px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '1px' },
                            { color: '#ffffff', fontSize: '32px', fontFamily: 'Georgia', fontStyle: 'italic' },
                            { color: '#FFD700', fontSize: '28px', fontFamily: 'Arial', fontWeight: '700' },
                            { color: '#ffffff', fontSize: '24px', fontFamily: 'Arial', fontWeight: '600' }
                        ],
                        textPositions: [
                            { x: 400, y: 100, align: 'center' },
                            { x: 400, y: 160, align: 'center' },
                            { x: 400, y: 220, align: 'center' },
                            { x: 400, y: 280, align: 'center' }
                        ]
                    }
                ]
            }
        };

        // Dynamic Purpose Registration - Add new purposes at runtime
        window.registerNewPurpose = function(purposeName, config) {
            if (!purposeName || !config) {
                console.error('registerNewPurpose: Missing required parameters');
                return false;
            }

            if (!config.defaultTexts || !config.templates) {
                console.error('registerNewPurpose: Config must have defaultTexts and templates');
                return false;
            }

            // Add to universal template config
            UNIVERSAL_TEMPLATE_CONFIG[purposeName] = config;

            console.log(`✅ Purpose "${purposeName}" registered successfully`);
            console.log(`Templates available: ${config.templates.length}`);

            return true;
        };

        // Get all available purposes
        window.getAvailablePurposes = function() {
            const purposes = Object.keys(UNIVERSAL_TEMPLATE_CONFIG);
            purposes.push('Anniversary'); // Anniversary uses its own system
            return purposes;
        };

        // Check if purpose uses universal template system
        window.isUniversalPurpose = function(purpose) {
            return !!UNIVERSAL_TEMPLATE_CONFIG[purpose];
        };

        // Template data organized by purpose
        const templateData = {
            'Anniversary': [
                { id: 'anniversary1', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-1.png', name: 'Anniversary Template 1' },
                { id: 'anniversary2', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-2.png', name: 'Anniversary Template 2' },
                { id: 'anniversary3', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-3.png', name: 'Anniversary Template 3' },
                { id: 'anniversary4', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-6.png', name: 'Anniversary Template 4' },
                { id: 'anniversary5', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-8.png', name: 'Anniversary Template 5' },
                { id: 'anniversary6', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-9.png', name: 'Anniversary Template 6' },
                { id: 'anniversary7', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-11.png', name: 'Anniversary Template 7' },
                { id: 'anniversary8', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-12.png', name: 'Anniversary Template 8' },
                { id: 'anniversary9', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-13.png', name: 'Anniversary Template 9' },
                { id: 'anniversary10', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-14.png', name: 'Anniversary Template 10' },
                { id: 'anniversary11', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-15.png', name: 'Anniversary Template 11' },
                { id: 'anniversary12', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-4.png', name: 'Anniversary Template 12' },
                { id: 'anniversary13', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-5.png', name: 'Anniversary Template 13' },
                { id: 'anniversary14', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-10.png', name: 'Anniversary Template 14' },
                { id: 'anniversary15', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-7.png', name: 'Anniversary Template 15' }
            ],
            'Benefit': [
                { id: 'benefit1', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-3.png', name: 'Benefit Template 1' },
                { id: 'benefit2', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-4.png', name: 'Benefit Template 2' },
                { id: 'benefit3', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-5.png', name: 'Benefit Template 3' },
                { id: 'benefit4', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-6.png', name: 'Benefit Template 4' },
                { id: 'benefit5', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-8.png', name: 'Benefit Template 5' },
                { id: 'benefit6', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-9.png', name: 'Benefit Template 6' },
                { id: 'benefit7', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-11.png', name: 'Benefit Template 7' },
                { id: 'benefit8', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-12.png', name: 'Benefit Template 8' },
                { id: 'benefit9', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-13.png', name: 'Benefit Template 9' },
                { id: 'benefit10', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-14.png', name: 'Benefit Template 10' },
                { id: 'benefit11', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-10.png', name: 'Benefit Template 11' },
                { id: 'benefit12', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-7.png', name: 'Benefit Template 12' },
                { id: 'benefit13', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-2.png', name: 'Benefit Template 13' },
                { id: 'benefit14', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-1.png', name: 'Benefit Template 14' },
                { id: 'benefit15', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-15.png', name: 'Benefit Template 15' }
            ],
            'Christian': [
                { id: 'christian1', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-1.png', name: 'Christian Template 1' },
                { id: 'christian2', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-2.png', name: 'Christian Template 2' },
                { id: 'christian3', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-3.png', name: 'Christian Template 3' },
                { id: 'christian4', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-4.png', name: 'Christian Template 4' },
                { id: 'christian5', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-5.png', name: 'Christian Template 5' },
                { id: 'christian6', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-6.png', name: 'Christian Template 6' },
                { id: 'christian7', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-8.png', name: 'Christian Template 7' },
                { id: 'christian8', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-9.png', name: 'Christian Template 8' },
                { id: 'christian9', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-10.png', name: 'Christian Template 9' },
                { id: 'christian10', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-11.png', name: 'Christian Template 10' },
                { id: 'christian11', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-12_.png', name: 'Christian Template 11' },
                { id: 'christian12', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-13.png', name: 'Christian Template 12' },
                { id: 'christian13', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-14.png', name: 'Christian Template 13' },
                { id: 'christian14', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-15.png', name: 'Christian Template 14' },
                { id: 'christian15', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christian-7.png', name: 'Christian Template 15' }
            ],
            'Graduation': [
                { id: 'graduation1', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-1.png', name: 'Graduation Template 1' },
                { id: 'graduation2', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-2.png', name: 'Graduation Template 2' },
                { id: 'graduation3', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-4.png', name: 'Graduation Template 3' },
                { id: 'graduation4', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-5.png', name: 'Graduation Template 4' },
                { id: 'graduation5', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-7.png', name: 'Graduation Template 5' },
                { id: 'graduation6', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-8.png', name: 'Graduation Template 6' },
                { id: 'graduation7', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-9.png', name: 'Graduation Template 7' },
                { id: 'graduation8', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-10.png', name: 'Graduation Template 8' },
                { id: 'graduation9', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-11.png', name: 'Graduation Template 9' },
                { id: 'graduation10', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-12.png', name: 'Graduation Template 10' },
                { id: 'graduation11', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-13.png', name: 'Graduation Template 11' },
                { id: 'graduation12', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-14.png', name: 'Graduation Template 12' },
                { id: 'graduation13', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-15.png', name: 'Graduation Template 13' },
                { id: 'graduation14', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-6.png', name: 'Graduation Template 14' },
                { id: 'graduation15', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Graduation-3.png', name: 'Graduation Template 15' }
            ],
            'Holiday': [
                { id: 'holiday1', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/4th-of-July-1.png', name: '4th of July Template 1' },
                { id: 'holiday2', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/4th-of-July-5.png', name: '4th of July Template 2' },
                { id: 'holiday3', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christmas-4.png', name: 'Christmas Template 1' },
                { id: 'holiday4', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Happy-New-Year-2.png', name: 'Happy New Year Template 1' },
                { id: 'holiday5', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Happy-New-Year-4.png', name: 'Happy New Year Template 2' },
                { id: 'holiday6', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Happy-New-Year-5.png', name: 'Happy New Year Template 3' },
                { id: 'holiday7', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/4th-of-July-2.png', name: '4th of July Template 3' },
                { id: 'holiday8', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/4th-of-July-3.png', name: '4th of July Template 4' },
                { id: 'holiday9', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/4th-of-July-4.png', name: '4th of July Template 5' },
                { id: 'holiday9a', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christmas-1.png', name: 'Christmas Template 2' },
                { id: 'holiday10', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christmas-2.png', name: 'Christmas Template 3' },
                { id: 'holiday11', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christmas-3.png', name: 'Christmas Template 4' },
                { id: 'holiday12', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Christmas-5.png', name: 'Christmas Template 5' },
                { id: 'holiday13', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Happy-New-Year-1.png', name: 'Happy New Year Template 4' },
                { id: 'holiday14', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Happy-New-Year-3.png', name: 'Happy New Year Template 5' }
            ],
            'Local School': [
                { id: 'localschool1', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Bluff-Dale-ISD.png', name: 'Bluff Dale ISD Template' },
                { id: 'localschool2', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Dublin-ISD_Green_Background.png', name: 'Dublin ISD Green Background Template' },
                { id: 'localschool3', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Dublin-ISD_Yellow_Background.png', name: 'Dublin ISD Yellow Background Template' },
                { id: 'localschool4', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Huckabay-ISD_Background.png', name: 'Huckabay ISD Background Template' },
                { id: 'localschool5', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Lingleville-ISD.png', name: 'Lingleville ISD Template 1' },
                { id: 'localschool6', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Lingleville-ISD.png', name: 'Lingleville ISD Template 2' },
                { id: 'localschool7', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Morgan-Mill-ISD.png', name: 'Morgan Mill ISD Template' },
                { id: 'localschool8', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Stephenville-ISD-Bees.png', name: 'Stephenville ISD Bees Template' },
                { id: 'localschool9', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Stephenville-ISD_Jackets-2.png', name: 'Stephenville ISD Jackets Template 1' },
                { id: 'localschool10', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Stephenville-ISD_Jackets.png', name: 'Stephenville ISD Jackets Template 2' },
                { id: 'localschool11', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Three-Way-ISD_Background.png', name: 'Three Way ISD Background Template' }
            ],
            'Love': [
                { id: 'love1', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-2.png', name: 'Love Template 1' },
                { id: 'love2', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-5.png', name: 'Love Template 2' },
                { id: 'love3', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-6.png', name: 'Love Template 3' },
                { id: 'love4', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-7.png', name: 'Love Template 4' },
                { id: 'love5', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-8.png', name: 'Love Template 5' },
                { id: 'love6', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-9.png', name: 'Love Template 6' },
                { id: 'love7', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-10.png', name: 'Love Template 7' },
                { id: 'love8', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-11.png', name: 'Love Template 8' },
                { id: 'love9', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-1.png', name: 'Love Template 9' },
                { id: 'love10', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-3.png', name: 'Love Template 10' },
                { id: 'love11', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-4.png', name: 'Love Template 11' },
                { id: 'love12', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-12.png', name: 'Love Template 12' },
                { id: 'love13', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-13.png', name: 'Love Template 13' },
                { id: 'love14', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-14.png', name: 'Love Template 14' },
                { id: 'love15', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Love-15.png', name: 'Love Template 15' }
            ],
            'Marry Me': [
                { id: 'marryme1', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Marry-Me-1.png', name: 'Marry Me Template 1' },
                { id: 'marryme2', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Marry-Me-2.png', name: 'Marry Me Template 2' },
                { id: 'marryme3', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Marry-Me-3.png', name: 'Marry Me Template 3' },
                { id: 'marryme4', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Marry-Me-4.png', name: 'Marry Me Template 4' },
                { id: 'marryme5', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Marry-Me-5.png', name: 'Marry Me Template 5' },
                { id: 'marryme6', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Marry-Me-6.png', name: 'Marry Me Template 6' },
                { id: 'marryme7', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Marry-Me-7.png', name: 'Marry Me Template 7' },
                { id: 'marryme8', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Marry-Me-8.png', name: 'Marry Me Template 8' },
                { id: 'marryme9', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Marry-Me-11.png', name: 'Marry Me Template 9' },
                { id: 'marryme10', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Marry-Me-14.png', name: 'Marry Me Template 10' },
                { id: 'marryme11', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Marry-Me-15.png', name: 'Marry Me Template 11' },
                { id: 'marryme12', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Marry-Me-10.png', name: 'Marry Me Template 12' },
                { id: 'marryme13', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Marry-Me-12.png', name: 'Marry Me Template 13' },
                { id: 'marryme14', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Marry-Me-13.png', name: 'Marry Me Template 14' },
                { id: 'marryme15', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Marry-Me-9.png', name: 'Marry Me Template 15' }
            ],
            'New Born': [
                { id: 'newborn1', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/New-Born-1.png', name: 'New Born Template 1' },
                { id: 'newborn2', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/New-Born-2.png', name: 'New Born Template 2' },
                { id: 'newborn3', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/New-Born-3.png', name: 'New Born Template 3' },
                { id: 'newborn4', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/New-Born-6.png', name: 'New Born Template 4' },
                { id: 'newborn5', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/New-Born-8.png', name: 'New Born Template 5' },
                { id: 'newborn6', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/New-Born-9.png', name: 'New Born Template 6' },
                { id: 'newborn7', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/New-Born-12.png', name: 'New Born Template 7' },
                { id: 'newborn8', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/New-Born-13.png', name: 'New Born Template 8' },
                { id: 'newborn9', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/New-Born-14.png', name: 'New Born Template 9' },
                { id: 'newborn10', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/New-Born-15.png', name: 'New Born Template 10' },
                { id: 'newborn11', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/New-Born-4.png', name: 'New Born Template 11' },
                { id: 'newborn12', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/New-Born-5.png', name: 'New Born Template 12' },
                { id: 'newborn13', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/New-Born-7.png', name: 'New Born Template 13' },
                { id: 'newborn14', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/New-Born-10.png', name: 'New Born Template 14' },
                { id: 'newborn15', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/New-Born-11.png', name: 'New Born Template 15' }
            ],
            'Obituary': [
                { id: 'obituary1', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Obituary-1.png', name: 'Obituary Template 1' },
                { id: 'obituary2', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Obituary-2.png', name: 'Obituary Template 2' },
                { id: 'obituary3', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Obituary-3.png', name: 'Obituary Template 3' },
                { id: 'obituary4', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Obituary-4.png', name: 'Obituary Template 4' },
                { id: 'obituary5', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Obituary-6.png', name: 'Obituary Template 5' },
                { id: 'obituary6', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Obituary-7.png', name: 'Obituary Template 6' },
                { id: 'obituary7', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Obituary-9.png', name: 'Obituary Template 7' },
                { id: 'obituary8', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Obituary-10.png', name: 'Obituary Template 8' },
                { id: 'obituary9', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Obituary-11.png', name: 'Obituary Template 9' },
                { id: 'obituary10', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Obituary-12.png', name: 'Obituary Template 10' },
                { id: 'obituary11', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Obituary-13.png', name: 'Obituary Template 11' },
                { id: 'obituary12', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Obituary-14.png', name: 'Obituary Template 12' },
                { id: 'obituary13', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Obituary-15.png', name: 'Obituary Template 13' },
                { id: 'obituary14', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Obituary-5.png', name: 'Obituary Template 14' },
                { id: 'obituary15', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Obituary-8.png', name: 'Obituary Template 15' }
            ],
            'Other': [
                { id: 'other1', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Other-2.png', name: 'Other Template 1' },
                { id: 'other2', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Other-3.png', name: 'Other Template 2' },
                { id: 'other3', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Other-7.png', name: 'Other Template 3' },
                { id: 'other4', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Other-11.png', name: 'Other Template 4' },
                { id: 'other5', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Other-12.png', name: 'Other Template 5' },
                { id: 'other6', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Other-13.png', name: 'Other Template 6' },
                { id: 'other7', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Other-14.png', name: 'Other Template 7' },
                { id: 'other8', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Other-15.png', name: 'Other Template 8' },
                { id: 'other9', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Other-1.png', name: 'Other Template 9' },
                { id: 'other10', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Other-4.png', name: 'Other Template 10' },
                { id: 'other11', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Other-5.png', name: 'Other Template 11' },
                { id: 'other12', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Other-6.png', name: 'Other Template 12' },
                { id: 'other13', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Other-8.png', name: 'Other Template 13' },
                { id: 'other14', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Other-9.png', name: 'Other Template 14' },
                { id: 'other15', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Other-10.png', name: 'Other Template 15' }
            ],
            'Pet': [
                { id: 'pet1', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Pet-2.png', name: 'Pet Template 1' },
                { id: 'pet2', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Pet-3.png', name: 'Pet Template 2' },
                { id: 'pet3', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Pet-4.png', name: 'Pet Template 3' },
                { id: 'pet4', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Pet-5.png', name: 'Pet Template 4' },
                { id: 'pet5', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Pet-6.png', name: 'Pet Template 5' },
                { id: 'pet6', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Pet-7.png', name: 'Pet Template 6' },
                { id: 'pet7', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Pet-8.png', name: 'Pet Template 7' },
                { id: 'pet8', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Pet-9.png', name: 'Pet Template 8' },
                { id: 'pet9', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Pet-10.png', name: 'Pet Template 9' },
                { id: 'pet10', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Pet-11.png', name: 'Pet Template 10' },
                { id: 'pet11', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Pet-12.png', name: 'Pet Template 11' },
                { id: 'pet12', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Pet-13.png', name: 'Pet Template 12' },
                { id: 'pet13', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Pet-14.png', name: 'Pet Template 13' },
                { id: 'pet14', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Pet-15.png', name: 'Pet Template 14' },
                { id: 'pet15', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Pet-1.png', name: 'Pet Template 15' }
            ],
            'Prayer': [
                { id: 'prayer1', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Prayer-1.png', name: 'Prayer Template 1' },
                { id: 'prayer2', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Prayer-2.png', name: 'Prayer Template 2' },
                { id: 'prayer3', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Prayer-3.png', name: 'Prayer Template 3' },
                { id: 'prayer4', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Prayer-4.png', name: 'Prayer Template 4' },
                { id: 'prayer5', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Prayer-5.png', name: 'Prayer Template 5' },
                { id: 'prayer6', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Prayer-7.png', name: 'Prayer Template 6' },
                { id: 'prayer7', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Prayer-8.png', name: 'Prayer Template 7' },
                { id: 'prayer8', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Prayer-11.png', name: 'Prayer Template 8' },
                { id: 'prayer9', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Prayer-12.png', name: 'Prayer Template 9' },
                { id: 'prayer10', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Prayer-13.png', name: 'Prayer Template 10' },
                { id: 'prayer11', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Prayer-14.png', name: 'Prayer Template 11' },
                { id: 'prayer12', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Prayer-15.png', name: 'Prayer Template 12' },
                { id: 'prayer13', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Prayer-10.png', name: 'Prayer Template 13' },
                { id: 'prayer14', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Prayer-6.png', name: 'Prayer Template 14' },
                { id: 'prayer15', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Prayer-9.png', name: 'Prayer Template 15' }
            ],
            'Retirement': [
                { id: 'retirement1', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Retirement-3.png', name: 'Retirement Template 1' },
                { id: 'retirement2', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Retirement-4.png', name: 'Retirement Template 2' },
                { id: 'retirement3', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Retirement-5.png', name: 'Retirement Template 3' },
                { id: 'retirement4', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Retirement-6.png', name: 'Retirement Template 4' },
                { id: 'retirement5', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Retirement-7.png', name: 'Retirement Template 5' },
                { id: 'retirement6', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Retirement-8.png', name: 'Retirement Template 6' },
                { id: 'retirement7', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Retirement-9.png', name: 'Retirement Template 7' },
                { id: 'retirement8', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Retirement-10.png', name: 'Retirement Template 8' },
                { id: 'retirement9', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Retirement-11.png', name: 'Retirement Template 9' },
                { id: 'retirement10', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Retirement-12.png', name: 'Retirement Template 10' },
                { id: 'retirement11', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Retirement-13.png', name: 'Retirement Template 11' },
                { id: 'retirement12', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Retirement-14.png', name: 'Retirement Template 12' },
                { id: 'retirement13', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Retirement-15.png', name: 'Retirement Template 13' },
                { id: 'retirement14', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Retirement-1.png', name: 'Retirement Template 14' },
                { id: 'retirement15', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Retirement-2.png', name: 'Retirement Template 15' }
            ],
            'Wedding': [
                { id: 'wedding1', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Wedding-1.png', name: 'Wedding Template 1' },
                { id: 'wedding2', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Wedding-2.png', name: 'Wedding Template 2' },
                { id: 'wedding3', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Wedding-3.png', name: 'Wedding Template 3' },
                { id: 'wedding4', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Wedding-4.png', name: 'Wedding Template 4' },
                { id: 'wedding5', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Wedding-5.png', name: 'Wedding Template 5' },
                { id: 'wedding6', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Wedding-6.png', name: 'Wedding Template 6' },
                { id: 'wedding7', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Wedding-7.png', name: 'Wedding Template 7' },
                { id: 'wedding8', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Wedding-8.png', name: 'Wedding Template 8' },
                { id: 'wedding9', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Wedding-10.png', name: 'Wedding Template 9' },
                { id: 'wedding10', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Wedding-11.png', name: 'Wedding Template 10' },
                { id: 'wedding11', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Wedding-12.png', name: 'Wedding Template 11' },
                { id: 'wedding12', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Wedding-13.png', name: 'Wedding Template 12' },
                { id: 'wedding13', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Wedding-15.png', name: 'Wedding Template 13' },
                { id: 'wedding14', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Wedding-14.png', name: 'Wedding Template 14' },
                { id: 'wedding15', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Wedding-9.png', name: 'Wedding Template 15' }
            ],
            'Welcome': [
                { id: 'welcome1', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Welcome-5.png', name: 'Welcome Template 1' },
                { id: 'welcome2', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Welcome-6.png', name: 'Welcome Template 2' },
                { id: 'welcome3', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Welcome-8.png', name: 'Welcome Template 3' },
                { id: 'welcome4', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Welcome-9.png', name: 'Welcome Template 4' },
                { id: 'welcome5', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Welcome-13.png', name: 'Welcome Template 5' },
                { id: 'welcome6', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Welcome-14.png', name: 'Welcome Template 6' },
                { id: 'welcome7', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Welcome-1.png', name: 'Welcome Template 7' },
                { id: 'welcome8', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Welcome-2.png', name: 'Welcome Template 8' },
                { id: 'welcome9', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Welcome-3.png', name: 'Welcome Template 9' },
                { id: 'welcome10', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Welcome-4.png', name: 'Welcome Template 10' },
                { id: 'welcome11', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Welcome-7.png', name: 'Welcome Template 11' },
                { id: 'welcome12', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Welcome-10.png', name: 'Welcome Template 12' },
                { id: 'welcome13', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Welcome-11.png', name: 'Welcome Template 13' },
                { id: 'welcome14', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Welcome-12.png', name: 'Welcome Template 14' },
                { id: 'welcome15', url: 'https://www.borgesmedia.com/wp-content/uploads/2025/06/Welcome-15.png', name: 'Welcome Template 15' }
            ],
            'Birthday': [
                // Placeholder for birthday templates - will be added later
            ],
            'Business Promotion': [
                // Placeholder for business templates - will be added later
            ]
        };

        // Function to generate template definitions dynamically
        function generateTemplateDefinition(templateId, templateUrl) {
            return {
                background: templateUrl,
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            };
        }

        // Function to get template definition (checks both static and dynamic templates)
        function getTemplate(templateId) {
            // Check if template exists in static templates object
            if (templates[templateId]) {
                return templates[templateId];
            }

            // Generate dynamic template definition
            // Find the template in templateData
            for (const purpose in templateData) {
                const purposeTemplates = templateData[purpose];
                const template = purposeTemplates.find(t => t.id === templateId);
                if (template) {
                    return generateTemplateDefinition(templateId, template.url);
                }
            }

            // Fallback to default
            return templates.anniversary1 || {
                background: '#ff6b6b',
                textPositions: [
                    { x: 400, y: 120, align: 'center' },
                    { x: 400, y: 180, align: 'center' },
                    { x: 400, y: 260, align: 'center' },
                    { x: 400, y: 320, align: 'center' }
                ]
            };
        }

        // Function to load templates based on purpose
        function loadTemplatesForPurpose(purpose) {
            const templateSelection = document.getElementById('templateSelection');
            const noTemplatesMessage = document.getElementById('noTemplatesMessage');

            // Clear existing templates
            templateSelection.innerHTML = '';

            if (!purpose || !templateData[purpose] || templateData[purpose].length === 0) {
                templateSelection.innerHTML = `
                    <div class="no-templates-message">
                        <h3>No templates available for ${purpose}</h3>
                        <p>Templates for this purpose are coming soon.</p>
                    </div>
                `;
                return;
            }

            // Hide the no templates message
            if (noTemplatesMessage) {
                noTemplatesMessage.style.display = 'none';
            }

            const templates = templateData[purpose];
            templates.forEach((template, index) => {
                const templateOption = document.createElement('div');
                templateOption.className = `template-option ${index === 0 ? 'active' : ''}`;
                templateOption.setAttribute('data-template', template.id);
                templateOption.setAttribute('data-purpose', purpose);

                const img = document.createElement('img');
                img.src = template.url;
                img.alt = template.name;
                img.style.width = '100%';
                img.style.height = '100%';
                img.style.objectFit = 'cover';

                templateOption.appendChild(img);
                templateSelection.appendChild(templateOption);

                // Add click event listener
                templateOption.addEventListener('click', function() {
                    document.querySelectorAll('.template-option').forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');
                    currentTemplate = this.getAttribute('data-template');
                    document.getElementById('selectedTemplate').value = currentTemplate;
                    updateBillboard();
                });
            });

            // Set the first template as current if available
            if (templates.length > 0) {
                currentTemplate = templates[0].id;
                document.getElementById('selectedTemplate').value = currentTemplate;
                updateBillboard();
            }
        }

        // Setup event listeners
        function setupEventListeners() {

            // Text inputs (legacy - may not exist in current HTML)
            ['line1', 'line2', 'line3', 'line4'].forEach(lineId => {
                const input = document.getElementById(lineId);
                if (input) {
                    input.addEventListener('input', updateBillboard);
                }
            });

            // Font selection
            const fontSelect = document.getElementById('fontSelect');
            if (fontSelect) {
                fontSelect.addEventListener('change', function() {
                    currentFont = this.value;
                    // Apply to all text lines
                    Object.keys(textSettings).forEach(line => {
                        textSettings[line].fontFamily = currentFont;
                    });
                    updateBillboard();
                });
            }

            // Background color
            const bgColorPicker = document.getElementById('bgColorPicker');
            const bgColorPreview = document.getElementById('bgColorPreview');
            const bgColorValue = document.getElementById('bgColorValue');

            if (bgColorPicker && bgColorPreview && bgColorValue) {
                bgColorPicker.addEventListener('input', function() {
                    currentBgColor = this.value;
                    bgColorPreview.style.backgroundColor = currentBgColor;
                    bgColorValue.textContent = currentBgColor;
                    updateBillboard();
                });

                bgColorPreview.addEventListener('click', function() {
                    bgColorPicker.click();
                });
            }

            // Background graphics
            document.querySelectorAll('.background-graphic-item').forEach(item => {
                item.addEventListener('click', function() {
                    document.querySelectorAll('.background-graphic-item').forEach(i => i.classList.remove('selected'));
                    this.classList.add('selected');
                    currentBgGraphic = this.getAttribute('data-bg');
                    document.getElementById('selectedBackground').value = currentBgGraphic;
                    updateBillboard();
                });
            });

            // Photo upload
            const photoUpload = document.getElementById('photoUpload');
            const photoInput = document.getElementById('photoInput');

            if (photoUpload && photoInput) {
                photoUpload.addEventListener('click', () => photoInput.click());
                photoInput.addEventListener('change', handlePhotoUpload);
            }

            // Setup color pickers and settings buttons
            setupColorPickers();
            setupSettingsButtons();

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.color-picker-dropdown')) {
                    document.querySelectorAll('.color-dropdown').forEach(dropdown => {
                        dropdown.classList.remove('show');
                    });
                }
            });
        }

        // Setup color pickers for each text line
        function setupColorPickers() {
            ['1', '2', '3', '4'].forEach(lineNum => {
                const colorBtn = document.getElementById(`colorBtn${lineNum}`);
                const colorDropdown = document.getElementById(`colorDropdown${lineNum}`);
                const colorPaletteEl = document.getElementById(`colorPalette${lineNum}`);
                const lineId = `line${lineNum}`;

                // Check if all elements exist
                if (!colorBtn || !colorDropdown || !colorPaletteEl) {
                    console.warn(`Missing elements for line ${lineNum}`);
                    return;
                }

                // Set initial color
                const initialColor = textSettings[lineId].color;
                colorBtn.style.backgroundColor = initialColor;

                // Create unified color picker
                createUnifiedColorPicker(colorPaletteEl, colorDropdown, lineId, setTextColor);

                // Color button click
                colorBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    // Close other dropdowns
                    document.querySelectorAll('.color-dropdown').forEach(dropdown => {
                        if (dropdown !== colorDropdown) {
                            dropdown.classList.remove('show');
                        }
                    });
                    colorDropdown.classList.toggle('show');
                });


            });
        }

        // Setup settings buttons
        function setupSettingsButtons() {
            ['1', '2', '3', '4'].forEach(lineNum => {
                const settingsBtn = document.getElementById(`settingsBtn${lineNum}`);
                if (settingsBtn) {
                    settingsBtn.addEventListener('click', () => {
                        openFontModal(`line${lineNum}`);
                    });
                }
            });
        }
        // Photo upload handler
        function handlePhotoUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            // Validate file format
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
            if (!allowedTypes.includes(file.type.toLowerCase())) {
                alert('Please upload a JPG or PNG image file only.');
                event.target.value = ''; // Clear the input
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    // Check image resolution
                    const width = img.naturalWidth;
                    const height = img.naturalHeight;
                    const minRecommendedWidth = 1200;
                    const minRecommendedHeight = 800;

                    let resolutionMessage = '';
                    if (width < minRecommendedWidth || height < minRecommendedHeight) {
                        resolutionMessage = `\n\nNote: Image resolution is ${width}x${height}px. For best quality, we recommend at least ${minRecommendedWidth}x${minRecommendedHeight}px.`;
                    }

                    uploadedImage = img;
                    updateBillboard();

                    // Update upload container
                    const photoUpload = document.getElementById('photoUpload');
                    photoUpload.innerHTML = `
                        <div class="upload-icon">✅</div>
                        <div class="upload-text">Image uploaded: ${file.name}</div>
                        <div class="upload-subtext">Click to change${resolutionMessage ? ' • Low resolution detected' : ''}</div>
                    `;

                    // Show resolution warning if needed
                    if (resolutionMessage) {
                        setTimeout(() => {
                            alert(`Image uploaded successfully!${resolutionMessage}`);
                        }, 100);
                    }
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        // Set text color for a specific line
        function setTextColor(lineId, color) {
            textSettings[lineId].color = color;
            const colorBtn = document.getElementById(`colorBtn${lineId.slice(-1)}`);
            colorBtn.style.backgroundColor = color;
            updateBillboard();
        }

        // Open font modal for specific text line
        function openFontModal(lineId) {
            currentEditingLine = lineId;
            const modal = document.getElementById('fontModal');
            const settings = textSettings[lineId];

            // Set current values in modal
            document.getElementById('modalFontSelect').value = settings.fontFamily;
            document.getElementById('modalFontSize').value = settings.fontSize;
            document.getElementById('modalShadow').checked = settings.shadow;
            document.getElementById('modalShadowWidth').value = settings.shadowWidth !== undefined ? settings.shadowWidth : 4;

            modal.style.display = 'flex';
        }

        // Make functions globally accessible
        window.applyFontSettings = function() {
            if (!currentEditingLine) return;

            const fontFamily = document.getElementById('modalFontSelect').value;
            const fontSize = parseInt(document.getElementById('modalFontSize').value);
            const shadow = document.getElementById('modalShadow').checked;
            const shadowWidth = parseInt(document.getElementById('modalShadowWidth').value);
            const applyToAll = document.getElementById('applyToAll').checked;

            if (applyToAll) {
                // Apply to all text lines
                Object.keys(textSettings).forEach(line => {
                    textSettings[line].fontFamily = fontFamily;
                    textSettings[line].fontSize = fontSize;
                    textSettings[line].shadow = shadow;
                    textSettings[line].shadowWidth = shadowWidth;
                });
            } else {
                // Apply only to current line
                textSettings[currentEditingLine].fontFamily = fontFamily;
                textSettings[currentEditingLine].fontSize = fontSize;
                textSettings[currentEditingLine].shadow = shadow;
                textSettings[currentEditingLine].shadowWidth = shadowWidth;
            }

            updateBillboard();
            window.closeFontModal();
        };

        window.closeFontModal = function() {
            const modal = document.getElementById('fontModal');
            if (modal) {
                modal.style.display = 'none';
            }
            currentEditingLine = null;
            const applyToAllCheckbox = document.getElementById('applyToAll');
            if (applyToAllCheckbox) {
                applyToAllCheckbox.checked = false;
            }
        };
        // Main billboard update function (now primarily for editor canvas)
        function updateBillboard() {
            try {
                // Only update if we're in editor mode and have editor canvas
                if (isEditorMode && editorCanvas && editorCtx) {
                    updateEditorCanvas();
                    return;
                }

                // For compatibility, save billboard data if needed
                saveBillboardData();

            } catch (error) {
                console.error('Error updating billboard:', error);
            }
        }

        // Draw background
        function drawBackground() {
            const template = getTemplate(currentTemplate);

            if (template && template.background) {
                if (template.background.includes('gradient')) {
                    // Parse gradient
                    const gradientMatch = template.background.match(/linear-gradient\(([^)]+)\)/);
                    if (gradientMatch) {
                        const parts = gradientMatch[1].split(',').map(s => s.trim());
                        const angle = parts[0];
                        const color1 = parts[1];
                        const color2 = parts[2];

                        // Create gradient
                        const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                        gradient.addColorStop(0, color1);
                        gradient.addColorStop(1, color2);

                        ctx.fillStyle = gradient;
                        ctx.fillRect(0, 0, canvas.width, canvas.height);
                    } else {
                        ctx.fillStyle = currentBgColor;
                        ctx.fillRect(0, 0, canvas.width, canvas.height);
                    }
                } else if (template.background.includes('http')) {
                    // Handle image background
                    const img = new Image();
                    img.crossOrigin = 'anonymous';
                    img.onload = function() {
                        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                        // Redraw text after image loads
                        setTimeout(() => {
                            drawTextLines();
                        }, 10);
                    };
                    img.src = template.background;
                } else {
                    ctx.fillStyle = currentBgColor;
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                }
            } else {
                ctx.fillStyle = currentBgColor;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
            }
        }

        // Draw uploaded image
        function drawUploadedImage() {
            const maxWidth = canvas.width * 0.3;
            const maxHeight = canvas.height * 0.4;

            let drawWidth = uploadedImage.width;
            let drawHeight = uploadedImage.height;

            // Scale image to fit
            if (drawWidth > maxWidth) {
                drawHeight = (drawHeight * maxWidth) / drawWidth;
                drawWidth = maxWidth;
            }

            if (drawHeight > maxHeight) {
                drawWidth = (drawWidth * maxHeight) / drawHeight;
                drawHeight = maxHeight;
            }

            // Position image (bottom right)
            const x = canvas.width - drawWidth - 20;
            const y = canvas.height - drawHeight - 20;

            ctx.drawImage(uploadedImage, x, y, drawWidth, drawHeight);
        }
        // Draw background graphic
        function drawBackgroundGraphic() {
            ctx.save();
            ctx.globalAlpha = 0.3;
            ctx.font = '100px Arial';
            ctx.textAlign = 'center';

            const graphics = {
                'balloons': '🎈',
                'stars': '⭐',
                'hearts': '❤️',
                'confetti': '🎉',
                'flowers': '🌸'
            };

            const graphic = graphics[currentBgGraphic];
            if (graphic) {
                // Draw multiple instances
                for (let i = 0; i < 5; i++) {
                    const x = Math.random() * canvas.width;
                    const y = Math.random() * canvas.height;
                    ctx.fillText(graphic, x, y);
                }
            }

            ctx.restore();
        }

        // Draw text lines
        function drawTextLines() {
            const template = getTemplate(currentTemplate);
            const textInputs = ['line1', 'line2', 'line3', 'line4'];

            textInputs.forEach((lineId, index) => {
                const input = document.getElementById(lineId);
                if (!input) return; // Skip if input doesn't exist

                const text = input.value;

                if (text.trim()) {
                    const settings = textSettings[lineId];
                    const position = template.textPositions[index];

                    ctx.save();

                    // Set font
                    ctx.font = `${settings.fontSize}px ${settings.fontFamily}`;
                    ctx.textAlign = position.align;
                    ctx.fillStyle = settings.color;

                    // Add shadow if enabled
                    if (settings.shadow) {
                        const shadowWidth = settings.shadowWidth !== undefined ? settings.shadowWidth : 4;
                        ctx.shadowColor = 'rgba(0,0,0,0.7)';
                        ctx.shadowBlur = shadowWidth;
                        ctx.shadowOffsetX = shadowWidth / 2;
                        ctx.shadowOffsetY = shadowWidth / 2;
                    }

                    // Draw text
                    ctx.fillText(text, position.x, position.y);

                    ctx.restore();
                }
            });
        }

        // Professional comprehensive state saving with complete UI restoration data
        function saveBillboardData() {
            console.log('saveBillboardData: Starting comprehensive data save with UI state...');

            // Get text lines from editor inputs (primary source) and regular inputs (fallback)
            const textLines = {};

            // First, try to get from editor inputs (these are the active ones when in editor mode)
            const editorInputs = ['editorLine1', 'editorLine2', 'editorLine3', 'editorLine4'];
            let hasEditorData = false;

            editorInputs.forEach((inputId, index) => {
                const element = document.getElementById(inputId);
                if (element && element.value && element.value.trim()) {
                    const lineKey = `line${index + 1}`;
                    textLines[lineKey] = element.value.trim();
                    hasEditorData = true;
                    console.log(`saveBillboardData: Captured from editor ${inputId} = "${element.value}"`);
                }
            });

            // If no editor data, fall back to regular inputs
            if (!hasEditorData) {
                const regularInputs = ['line1', 'line2', 'line3', 'line4'];
                regularInputs.forEach(inputId => {
                    const element = document.getElementById(inputId);
                    if (element && element.value && element.value.trim()) {
                        textLines[inputId] = element.value.trim();
                        console.log(`saveBillboardData: Captured from regular ${inputId} = "${element.value}"`);
                    }
                });
            }

            // Also save editor-specific mapping for restoration
            const editorTextLines = {};
            editorInputs.forEach(inputId => {
                const element = document.getElementById(inputId);
                if (element && element.value && element.value.trim()) {
                    editorTextLines[inputId] = element.value.trim();
                }
            });

            // Get current template from multiple sources
            const currentTemplate = window.editorState?.currentAnniversaryTemplate ||
                                localStorage.getItem('currentAnniversaryTemplate') ||
                                document.getElementById('selectedTemplate')?.value ||
                                'default';

            // Get background settings from multiple sources
            const bgColor = window.editorState?.selectedBackgroundColor ||
                        localStorage.getItem('selectedBackgroundColor') ||
                        currentBgColor ||
                        '#000000';

            const bgGraphic = window.editorState?.selectedAnniversaryBackground ||
                            localStorage.getItem('selectedAnniversaryBackground') ||
                            currentBgGraphic ||
                            'Anniversary-1';

            const bgType = window.editorState?.selectedBackgroundType ||
                        localStorage.getItem('selectedBackgroundType') ||
                        'image';

            // Capture comprehensive text settings including colors and fonts for each line
            const comprehensiveTextSettings = {};
            ['editorLine1', 'editorLine2', 'editorLine3', 'editorLine4'].forEach(lineId => {
                const colorBtn = document.getElementById(lineId.replace('editorLine', 'editorColorBtn'));
                const colorValue = colorBtn ? colorBtn.style.backgroundColor : '#ffffff';

                comprehensiveTextSettings[lineId] = {
                    color: colorValue || '#ffffff',
                    fontSize: window.editorState?.editorTextSettings?.[lineId]?.fontSize || 50,
                    fontFamily: window.editorState?.editorTextSettings?.[lineId]?.fontFamily || 'Arial',
                    shadow: window.editorState?.editorTextSettings?.[lineId]?.shadow !== false,
                    shadowWidth: window.editorState?.editorTextSettings?.[lineId]?.shadowWidth || 4
                };
            });

            // Capture uploaded image data from multiple sources
            const uploadedImageData = window.editorState?.editorUploadedImage?.src ||
                                    (uploadedImage ? uploadedImage.src : null);

            // Get current canvas state from multiple possible canvases
            let canvasDataURL = null;
            const canvasSources = ['billboardCanvas', 'editorCanvas'];

            for (const canvasId of canvasSources) {
                const canvas = document.getElementById(canvasId);
                if (canvas) {
                    try {
                        canvasDataURL = canvas.toDataURL('image/png', 0.9);
                        console.log(`saveBillboardData: Canvas image captured from ${canvasId}`);
                        break;
                    } catch (e) {
                        console.warn(`saveBillboardData: Failed to capture from ${canvasId}:`, e);
                    }
                }
            }

            // Capture color picker button states for restoration
            const colorPickerStates = {};
            ['editorColorBtn1', 'editorColorBtn2', 'editorColorBtn3', 'editorColorBtn4'].forEach(btnId => {
                const btn = document.getElementById(btnId);
                if (btn) {
                    colorPickerStates[btnId] = btn.style.backgroundColor || '#ffffff';
                }
            });

            // Create comprehensive data object with complete UI restoration data
            const data = {
                template: currentTemplate,
                textLines: textLines,
                editorTextLines: editorTextLines, // Separate mapping for editor inputs
                bgColor: bgColor,
                bgGraphic: bgGraphic,
                bgType: bgType,
                textSettings: comprehensiveTextSettings,
                hasUploadedImage: !!uploadedImageData,
                uploadedImageData: uploadedImageData,
                canvasImage: canvasDataURL,
                timestamp: new Date().toISOString(),
                // Enhanced UI state with complete restoration data
                uiState: {
                    selectedTemplate: currentTemplate,
                    selectedBackground: bgGraphic,
                    selectedBackgroundType: bgType,
                    backgroundColor: bgColor,
                    isCustomizing: true,
                    editorWasOpen: document.getElementById('templateEditorContainer')?.classList.contains('active') || false,
                    // Color picker states for visual restoration
                    colorPickerStates: colorPickerStates,
                    // Font settings for each line
                    fontSettings: comprehensiveTextSettings,
                    // Background selection state
                    backgroundSelection: {
                        type: bgType,
                        graphic: bgGraphic,
                        color: bgColor
                    }
                }
            };

            console.log('saveBillboardData: Saving comprehensive design state with UI restoration data:', data);

            const billboardDataField = document.getElementById('billboardData');
            if (billboardDataField) {
                billboardDataField.value = JSON.stringify(data);
            }

            // Store in multiple localStorage keys for different purposes and easy restoration
            localStorage.setItem('adDesignData', JSON.stringify(data));
            localStorage.setItem('billboardCanvasImage', canvasDataURL || '');
            localStorage.setItem('billboardUIState', JSON.stringify(data.uiState));
            localStorage.setItem('billboardTextLines', JSON.stringify(data.textLines));
            localStorage.setItem('billboardSettings', JSON.stringify({
                template: data.template,
                bgColor: data.bgColor,
                bgGraphic: data.bgGraphic,
                bgType: data.bgType,
                textSettings: data.textSettings
            }));

            // Store individual settings for easier restoration
            localStorage.setItem('editorTextSettings', JSON.stringify(comprehensiveTextSettings));
            localStorage.setItem('editorColorStates', JSON.stringify(colorPickerStates));
            localStorage.setItem('editorBackgroundState', JSON.stringify(data.uiState.backgroundSelection));

            console.log('saveBillboardData: All design data and UI state saved to localStorage');
        }

        // Function to proceed to checkout - Professional implementation
        function proceedToCheckout() {
            try {
                console.log('proceedToCheckout: Starting checkout process...');

                // Force canvas update before saving
                if (window.updateEditorCanvasGlobal) {
                    window.updateEditorCanvasGlobal();
                    console.log('proceedToCheckout: Editor canvas updated');
                } else if (window.updateCanvas) {
                    window.updateCanvas();
                    console.log('proceedToCheckout: Canvas updated');
                }

                // Wait a moment for canvas to update, then save
                setTimeout(() => {
                    // Save current billboard data
                    saveBillboardData();

                    // Continue with checkout process
                    continueCheckoutProcess();
                }, 300);

                return; // Exit here, continue in continueCheckoutProcess
            } catch (error) {
                console.error('proceedToCheckout: Error occurred:', error);
                alert('An error occurred while preparing checkout. Please try again.');

                // Reset button state
                const button = document.querySelector('.submit-button');
                if (button) {
                    button.textContent = 'Continue to Checkout';
                    button.disabled = false;
                }
            }
        }

        // Continue checkout process after canvas update
        function continueCheckoutProcess() {
            try {
                console.log('continueCheckoutProcess: Continuing after canvas update...');

                // Save current billboard data again to ensure latest state
                saveBillboardData();

                // Comprehensive data validation
                const requiredData = {
                    purpose: localStorage.getItem('selectedPurpose'),
                    location: localStorage.getItem('billboardLocation'),
                    dates: localStorage.getItem('runDates'),
                    state: localStorage.getItem('selectedState'),
                    city: localStorage.getItem('selectedCity')
                };

                // Professional validation with detailed checking
                const missingFields = [];
                const fieldLabels = {
                    purpose: 'Purpose (Anniversary, Business, etc.)',
                    location: 'Billboard Location',
                    dates: 'Run Dates',
                    state: 'State',
                    city: 'City'
                };

                Object.keys(requiredData).forEach(field => {
                    const value = requiredData[field];
                    if (!value ||
                        value === 'Select a Purpose' ||
                        value === 'Not selected' ||
                        value.trim() === '' ||
                        value === 'undefined' ||
                        value === 'null') {
                        missingFields.push(fieldLabels[field] || field);
                    }
                });

                console.log('proceedToCheckout: Validation check:', {
                    requiredData: requiredData,
                    missingFields: missingFields
                });

                if (missingFields.length > 0) {
                    const message = `Please complete the following required information before proceeding to checkout:\n\n• ${missingFields.join('\n• ')}\n\nPlease go back to the form above and fill in all required fields.`;
                    alert(message);
                    console.warn('proceedToCheckout: Missing required fields:', missingFields);

                    // Scroll to top to help user see the form
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                    return;
                }

                // Professional canvas image capture with multiple fallbacks
                let canvasDataURL = null;
                const canvasSources = ['billboardCanvas', 'editorCanvas'];

                console.log('proceedToCheckout: Attempting to capture canvas image...');

                for (const canvasId of canvasSources) {
                    const canvas = document.getElementById(canvasId);
                    if (canvas) {
                        try {
                            // Ensure canvas has content before capturing
                            const ctx = canvas.getContext('2d');
                            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                            const hasContent = imageData.data.some(pixel => pixel !== 0);

                            if (hasContent) {
                                canvasDataURL = canvas.toDataURL('image/png', 0.9);
                                localStorage.setItem('adPreviewImage', canvasDataURL);
                                localStorage.setItem('billboardCanvasImage', canvasDataURL);
                                console.log(`proceedToCheckout: Canvas image captured from ${canvasId} (${canvasDataURL.length} bytes)`);
                                break;
                            } else {
                                console.warn(`proceedToCheckout: Canvas ${canvasId} appears to be empty`);
                            }
                        } catch (canvasError) {
                            console.warn(`proceedToCheckout: Failed to get image from ${canvasId}:`, canvasError);
                        }
                    }
                }

                // If no canvas image, try to generate one from current design data
                if (!canvasDataURL) {
                    console.log('proceedToCheckout: No canvas image found, attempting to generate preview...');
                    canvasDataURL = generatePreviewFromDesignData();
                    if (canvasDataURL) {
                        localStorage.setItem('adPreviewImage', canvasDataURL);
                        localStorage.setItem('billboardCanvasImage', canvasDataURL);
                        console.log('proceedToCheckout: Generated preview from design data');
                    }
                }

                if (!canvasDataURL) {
                    console.warn('proceedToCheckout: No canvas image could be generated - will use design data for preview');
                }

                // Store checkout timestamp for tracking
                localStorage.setItem('checkoutInitiated', new Date().toISOString());

                // Store current page URL for back navigation
                localStorage.setItem('designPageURL', window.location.href);

                // Set flag to indicate we're going to checkout (for return detection)
                localStorage.setItem('wentToCheckout', 'true');

                // Store editor state to ensure it reopens on return
                const editorContainer = document.getElementById('templateEditorContainer');
                if (editorContainer && editorContainer.classList.contains('active')) {
                    localStorage.setItem('editorWasOpen', 'true');
                    console.log('proceedToCheckout: Stored editor open state');
                }

                // Log successful data preparation
                console.log('proceedToCheckout: Data validation successful:', {
                    purpose: requiredData.purpose,
                    location: requiredData.location,
                    dates: requiredData.dates,
                    hasCanvasImage: !!canvasDataURL,
                    adDesignData: !!localStorage.getItem('adDesignData')
                });

                // Show loading state
                const button = document.querySelector('.submit-button');
                if (button) {
                    button.textContent = 'Redirecting to Checkout...';
                    button.disabled = true;
                }

                // Redirect to checkout page with slight delay for UX
                setTimeout(() => {
                    console.log('proceedToCheckout: Redirecting to checkout page...');
                    window.location.href = 'https://www.borgesmedia.com/billboard-checkout/';
                }, 500);

            } catch (error) {
                console.error('continueCheckoutProcess: Error occurred:', error);
                alert('An error occurred while preparing checkout. Please try again.');

                // Reset button state
                const button = document.querySelector('.submit-button');
                if (button) {
                    button.textContent = 'Continue to Checkout';
                    button.disabled = false;
                }
            }
        }

        // Make proceedToCheckout globally accessible
        window.proceedToCheckout = proceedToCheckout;

        // Professional debugging function for localStorage data
        window.debugCheckoutData = function() {
            console.log('=== CHECKOUT DATA DEBUG ===');
            const allData = {
                selectedPurpose: localStorage.getItem('selectedPurpose'),
                selectedState: localStorage.getItem('selectedState'),
                selectedCity: localStorage.getItem('selectedCity'),
                billboardLocation: localStorage.getItem('billboardLocation'),
                runDates: localStorage.getItem('runDates'),
                adDesignData: localStorage.getItem('adDesignData'),
                adPreviewImage: !!localStorage.getItem('adPreviewImage'),
                checkoutInitiated: localStorage.getItem('checkoutInitiated')
            };

            console.log('All localStorage data:', allData);

            const missingRequired = [];
            ['selectedPurpose', 'billboardLocation', 'runDates'].forEach(key => {
                if (!allData[key] || allData[key] === 'Select a Purpose' || allData[key] === 'Not selected') {
                    missingRequired.push(key);
                }
            });

            console.log('Missing required fields:', missingRequired);
            console.log('Ready for checkout:', missingRequired.length === 0);
            console.log('=== END DEBUG ===');

            return allData;
        };

        // Professional canvas preview generation from design data
        window.generatePreviewFromDesignData = function() {
            console.log('generatePreviewFromDesignData: Creating preview from design data...');

            try {
                const designData = localStorage.getItem('adDesignData');
                if (!designData) {
                    console.log('generatePreviewFromDesignData: No design data available');
                    return null;
                }

                const data = JSON.parse(designData);
                console.log('generatePreviewFromDesignData: Using design data:', data);

                // Create a temporary canvas for preview generation
                const tempCanvas = document.createElement('canvas');
                tempCanvas.width = 800;
                tempCanvas.height = 400;
                const ctx = tempCanvas.getContext('2d');

                // Set background
                if (data.bgColor) {
                    ctx.fillStyle = data.bgColor;
                    ctx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
                } else {
                    // Default gradient background
                    const gradient = ctx.createLinearGradient(0, 0, tempCanvas.width, tempCanvas.height);
                    gradient.addColorStop(0, '#0D7EE8');
                    gradient.addColorStop(1, '#0A6AC7');
                    ctx.fillStyle = gradient;
                    ctx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
                }

                // Add text lines if available
                if (data.textLines) {
                    ctx.fillStyle = 'white';
                    ctx.textAlign = 'center';
                    ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
                    ctx.shadowBlur = 4;
                    ctx.shadowOffsetX = 2;
                    ctx.shadowOffsetY = 2;

                    const lines = Object.values(data.textLines).filter(line => line && line.trim());
                    const lineHeight = 60;
                    const startY = (tempCanvas.height - (lines.length * lineHeight)) / 2 + 30;

                    lines.forEach((line, index) => {
                        ctx.font = 'bold 48px Arial';
                        ctx.fillText(line, tempCanvas.width / 2, startY + (index * lineHeight));
                    });
                }

                // Add template indicator
                if (data.template) {
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'right';
                    ctx.shadowBlur = 0;
                    ctx.fillText(`Template: ${data.template}`, tempCanvas.width - 20, tempCanvas.height - 20);
                }

                const previewDataURL = tempCanvas.toDataURL('image/png', 0.8);
                console.log('generatePreviewFromDesignData: Preview generated successfully');
                return previewDataURL;

            } catch (error) {
                console.error('generatePreviewFromDesignData: Error generating preview:', error);
                return null;
            }
        };

        // Professional state restoration function
        window.restoreBillboardDesign = function() {
            console.log('restoreBillboardDesign: Starting state restoration...');

            try {
                const savedData = localStorage.getItem('adDesignData');
                if (!savedData) {
                    console.log('restoreBillboardDesign: No saved design data found');
                    return false;
                }

                const designData = JSON.parse(savedData);
                console.log('restoreBillboardDesign: Found saved design data:', designData);

                // Restore text lines to both regular and editor inputs
                if (designData.textLines) {
                    Object.keys(designData.textLines).forEach(lineKey => {
                        const element = document.getElementById(lineKey);
                        if (element && designData.textLines[lineKey]) {
                            element.value = designData.textLines[lineKey];
                            console.log(`restoreBillboardDesign: Restored ${lineKey} = "${designData.textLines[lineKey]}"`);
                        }
                    });
                }

                // Restore editor text lines specifically
                if (designData.editorTextLines) {
                    Object.keys(designData.editorTextLines).forEach(editorLineKey => {
                        const element = document.getElementById(editorLineKey);
                        if (element && designData.editorTextLines[editorLineKey]) {
                            element.value = designData.editorTextLines[editorLineKey];
                            console.log(`restoreBillboardDesign: Restored editor ${editorLineKey} = "${designData.editorTextLines[editorLineKey]}"`);
                        }
                    });
                } else if (designData.textLines) {
                    // Fallback: map regular text lines to editor inputs
                    ['line1', 'line2', 'line3', 'line4'].forEach((lineKey, index) => {
                        if (designData.textLines[lineKey]) {
                            const editorElement = document.getElementById(`editorLine${index + 1}`);
                            if (editorElement) {
                                editorElement.value = designData.textLines[lineKey];
                                console.log(`restoreBillboardDesign: Mapped ${lineKey} to editorLine${index + 1} = "${designData.textLines[lineKey]}"`);
                            }
                        }
                    });
                }

                // Restore global variables if they exist in scope
                if (typeof currentTemplate !== 'undefined' && designData.template) {
                    currentTemplate = designData.template;
                }
                if (typeof currentBgColor !== 'undefined' && designData.bgColor) {
                    currentBgColor = designData.bgColor;
                }
                if (typeof currentBgGraphic !== 'undefined' && designData.bgGraphic) {
                    currentBgGraphic = designData.bgGraphic;
                }
                // Note: textSettings is const, so we can't reassign it directly
                // Instead, we'll update the individual properties or use the global state
                if (typeof textSettings !== 'undefined' && designData.textSettings) {
                    // Update individual properties of textSettings object
                    Object.keys(designData.textSettings).forEach(lineId => {
                        if (textSettings[lineId]) {
                            Object.assign(textSettings[lineId], designData.textSettings[lineId]);
                        }
                    });
                    console.log('restoreBillboardDesign: Text settings properties updated');
                }
                if (typeof uploadedImage !== 'undefined' && designData.uploadedImageData) {
                    // Restore uploaded image
                    const img = new Image();
                    img.onload = function() {
                        uploadedImage = img;
                        console.log('restoreBillboardDesign: Uploaded image restored');
                        // Redraw canvas after image loads
                        if (window.updateCanvas) {
                            updateCanvas();
                        }
                    };
                    img.src = designData.uploadedImageData;
                }

                // Restore canvas if available
                if (designData.canvasImage) {
                    const canvas = document.getElementById('billboardCanvas');
                    if (canvas) {
                        const ctx = canvas.getContext('2d');
                        const img = new Image();
                        img.onload = function() {
                            ctx.clearRect(0, 0, canvas.width, canvas.height);
                            ctx.drawImage(img, 0, 0);
                            console.log('restoreBillboardDesign: Canvas image restored');
                        };
                        img.src = designData.canvasImage;
                    }
                }

                // Update hidden fields
                const selectedTemplateField = document.getElementById('selectedTemplate');
                if (selectedTemplateField && designData.template) {
                    selectedTemplateField.value = designData.template;
                }

                const billboardDataField = document.getElementById('billboardData');
                if (billboardDataField) {
                    billboardDataField.value = JSON.stringify(designData);
                }

                // Special handling for anniversary templates with comprehensive UI restoration
                if (designData.template && designData.template.includes('anniversary')) {
                    console.log('restoreBillboardDesign: Detected anniversary template, restoring complete editor state...');

                    // Restore anniversary template editor state - BOTH global and local variables
                    if (window.editorState) {
                        // Set global state
                        window.editorState.currentAnniversaryTemplate = designData.template;
                        window.editorState.selectedAnniversaryBackground = designData.bgGraphic || 'Anniversary-1';
                        window.editorState.selectedBackgroundType = designData.bgType || 'image';
                        window.editorState.selectedBackgroundColor = designData.bgColor || '#ff0000';
                        window.editorState.isEditorMode = true;

                        // CRITICAL: Also set local variables that the canvas functions depend on
                        if (typeof currentAnniversaryTemplate !== 'undefined') {
                            currentAnniversaryTemplate = designData.template;
                        }
                        if (typeof selectedAnniversaryBackground !== 'undefined') {
                            selectedAnniversaryBackground = designData.bgGraphic || 'Anniversary-1';
                        }
                        if (typeof selectedBackgroundType !== 'undefined') {
                            selectedBackgroundType = designData.bgType || 'image';
                        }
                        if (typeof selectedBackgroundColor !== 'undefined') {
                            selectedBackgroundColor = designData.bgColor || '#ff0000';
                        }
                        if (typeof isEditorMode !== 'undefined') {
                            isEditorMode = true;
                        }

                        console.log('restoreBillboardDesign: Both global and local editor state restored');

                        // Force restore local variables using the global function
                        if (window.forceRestoreLocalVariables) {
                            window.forceRestoreLocalVariables(designData);
                        }

                        // Restore editor text lines
                        if (designData.textLines) {
                            ['editorLine1', 'editorLine2', 'editorLine3', 'editorLine4'].forEach((editorId, index) => {
                                const lineKey = `line${index + 1}`;
                                const editorElement = document.getElementById(editorId);
                                if (editorElement && designData.textLines[lineKey]) {
                                    editorElement.value = designData.textLines[lineKey];
                                    console.log(`restoreBillboardDesign: Restored ${editorId} = "${designData.textLines[lineKey]}"`);
                                }
                            });
                        }

                        // Restore color picker button states
                        if (designData.uiState && designData.uiState.colorPickerStates) {
                            Object.keys(designData.uiState.colorPickerStates).forEach(btnId => {
                                const btn = document.getElementById(btnId);
                                if (btn) {
                                    btn.style.backgroundColor = designData.uiState.colorPickerStates[btnId];
                                    console.log(`restoreBillboardDesign: Restored color picker ${btnId} = ${designData.uiState.colorPickerStates[btnId]}`);
                                }
                            });
                        }

                        // Restore text settings to global state and apply to UI
                        if (designData.textSettings) {
                            window.editorState.editorTextSettings = designData.textSettings;

                            // Apply text settings to color picker buttons
                            Object.keys(designData.textSettings).forEach(lineId => {
                                const settings = designData.textSettings[lineId];
                                const lineNum = lineId.replace('editorLine', '');
                                const colorBtn = document.getElementById(`editorColorBtn${lineNum}`);
                                if (colorBtn && settings.color) {
                                    colorBtn.style.backgroundColor = settings.color;
                                    console.log(`restoreBillboardDesign: Restored color picker ${lineId} = ${settings.color}`);
                                }
                            });

                            console.log('restoreBillboardDesign: Restored text settings to global state and UI');
                        }

                        // Restore background selection visual state
                        if (designData.bgGraphic) {
                            // Mark the correct background as selected
                            setTimeout(() => {
                                const backgroundOptions = document.querySelectorAll('.anniversary-bg-option');
                                backgroundOptions.forEach(option => {
                                    option.classList.remove('selected');
                                    if (option.dataset.background === designData.bgGraphic) {
                                        option.classList.add('selected');
                                        console.log(`restoreBillboardDesign: Selected background graphic ${designData.bgGraphic}`);
                                    }
                                });
                            }, 200);
                        }

                        // Restore background type selection
                        if (designData.bgType) {
                            setTimeout(() => {
                                const typeButtons = document.querySelectorAll('.background-type-btn');
                                typeButtons.forEach(btn => {
                                    btn.classList.remove('active');
                                    if (btn.dataset.type === designData.bgType) {
                                        btn.classList.add('active');
                                        console.log(`restoreBillboardDesign: Selected background type ${designData.bgType}`);
                                    }
                                });
                            }, 200);
                        }

                        // Restore uploaded image if available - ENHANCED VERSION
                        if (designData.uploadedImageData) {
                            console.log('restoreBillboardDesign: Starting uploaded image restoration...');
                            const img = new Image();
                            img.crossOrigin = 'anonymous'; // Handle CORS issues
                            img.onload = function() {
                                // Set in global state
                                window.editorState.editorUploadedImage = img;

                                // Also set in local variable if accessible
                                if (typeof editorUploadedImage !== 'undefined') {
                                    editorUploadedImage = img;
                                }

                                console.log('restoreBillboardDesign: Uploaded image loaded and set in both global and local state');

                                // Update upload container visual state
                                const uploadContainer = document.getElementById('editorPhotoUpload');
                                if (uploadContainer) {
                                    uploadContainer.style.border = '2px solid #28a745';
                                    uploadContainer.style.backgroundColor = '#d4edda';
                                    const uploadText = uploadContainer.querySelector('.upload-text');
                                    if (uploadText) {
                                        uploadText.textContent = 'Photo uploaded ✓ Click to change';
                                        uploadText.style.color = '#155724';
                                    }
                                }

                                // Force sync editor state
                                if (window.syncEditorState) {
                                    window.syncEditorState();
                                }

                                // Update canvas after image loads with multiple attempts
                                setTimeout(() => {
                                    if (window.updateEditorCanvasGlobal) {
                                        window.updateEditorCanvasGlobal();
                                        console.log('restoreBillboardDesign: Canvas updated after image load (attempt 1)');
                                    }
                                }, 100);

                                setTimeout(() => {
                                    if (window.updateEditorCanvasGlobal) {
                                        window.updateEditorCanvasGlobal();
                                        console.log('restoreBillboardDesign: Canvas updated after image load (attempt 2)');
                                    }
                                }, 300);
                            };
                            img.onerror = function() {
                                console.error('restoreBillboardDesign: Failed to load uploaded image:', designData.uploadedImageData.substring(0, 100) + '...');
                            };
                            img.src = designData.uploadedImageData;
                        }

                        // Restore template selection visual state
                        if (designData.template) {
                            setTimeout(() => {
                                const templateCards = document.querySelectorAll('.anniversary-template-card');
                                templateCards.forEach(card => {
                                    card.classList.remove('selected');
                                    if (card.dataset.template === designData.template) {
                                        card.classList.add('selected');
                                        console.log(`restoreBillboardDesign: Selected template ${designData.template}`);
                                    }
                                });
                            }, 200);
                        }

                        // Force canvas update with multiple attempts to ensure rendering
                        const forceCanvasUpdate = () => {
                            console.log('restoreBillboardDesign: Forcing canvas update...');

                            // Sync editor state first
                            if (window.syncEditorState) {
                                window.syncEditorState();
                            }

                            // Try multiple canvas update methods
                            if (window.updateEditorCanvasGlobal) {
                                window.updateEditorCanvasGlobal();
                                console.log('restoreBillboardDesign: Called updateEditorCanvasGlobal');
                            }

                            if (window.updateEditorCanvas) {
                                window.updateEditorCanvas();
                                console.log('restoreBillboardDesign: Called updateEditorCanvas');
                            }

                            if (window.updateCanvas) {
                                window.updateCanvas();
                                console.log('restoreBillboardDesign: Called updateCanvas');
                            }

                            // Direct canvas update as fallback - ENHANCED VERSION
                            const canvas = document.getElementById('editorCanvas');
                            if (canvas && window.editorState) {
                                const ctx = canvas.getContext('2d');
                                if (ctx) {
                                    console.log('restoreBillboardDesign: Starting direct canvas update fallback');

                                    // Set canvas size to match container
                                    const container = canvas.parentElement;
                                    if (container) {
                                        const containerRect = container.getBoundingClientRect();
                                        canvas.width = containerRect.width;
                                        canvas.height = containerRect.height;
                                    }

                                    // Clear and redraw
                                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                                    // Get template information
                                    const template = window.editorState.anniversaryTemplates &&
                                                window.editorState.anniversaryTemplates[window.editorState.currentAnniversaryTemplate];

                                    // Draw background
                                    if (window.editorState.selectedBackgroundType === 'color') {
                                        ctx.fillStyle = window.editorState.selectedBackgroundColor;
                                        ctx.fillRect(0, 0, canvas.width, canvas.height);
                                        console.log('restoreBillboardDesign: Color background drawn');

                                        // Draw text and uploaded image immediately for color background
                                        drawEditorTextDirect(ctx, canvas);
                                        drawEditorImageDirect(ctx, canvas, template);
                                    } else if (window.editorState.selectedAnniversaryBackground) {
                                        // Try to load and draw background image
                                        const bgImg = new Image();
                                        bgImg.crossOrigin = 'anonymous';
                                        bgImg.onload = function() {
                                            ctx.drawImage(bgImg, 0, 0, canvas.width, canvas.height);
                                            console.log('restoreBillboardDesign: Background image drawn');

                                            // Draw text and uploaded image after background loads
                                            drawEditorTextDirect(ctx, canvas);
                                            drawEditorImageDirect(ctx, canvas, template);
                                        };
                                        bgImg.onerror = function() {
                                            console.error('restoreBillboardDesign: Failed to load background image, drawing text anyway');
                                            // Still draw text and uploaded image even if background fails
                                            drawEditorTextDirect(ctx, canvas);
                                            drawEditorImageDirect(ctx, canvas, template);
                                        };
                                        // Try multiple possible paths for background image
                                        const bgPaths = [
                                            `https://www.borgesmedia.com/wp-content/uploads/2025/06/${window.editorState.selectedAnniversaryBackground}.png`,
                                            `/wp-content/uploads/2024/12/${window.editorState.selectedAnniversaryBackground}.jpg`,
                                            `/wp-content/uploads/2025/06/${window.editorState.selectedAnniversaryBackground}.png`
                                        ];

                                        let pathIndex = 0;
                                        const tryNextPath = () => {
                                            if (pathIndex < bgPaths.length) {
                                                bgImg.src = bgPaths[pathIndex];
                                                pathIndex++;
                                            }
                                        };

                                        bgImg.onerror = () => {
                                            console.warn(`restoreBillboardDesign: Failed to load background from ${bgImg.src}`);
                                            if (pathIndex < bgPaths.length) {
                                                tryNextPath();
                                            } else {
                                                console.error('restoreBillboardDesign: All background paths failed, drawing text anyway');
                                                drawEditorTextDirect(ctx, canvas);
                                                drawEditorImageDirect(ctx, canvas, template);
                                            }
                                        };

                                        tryNextPath(); // Start with first path
                                    }

                                    console.log('restoreBillboardDesign: Direct canvas update attempted');
                                }
                            }
                        };

                        // Multiple update attempts with increasing delays
                        setTimeout(forceCanvasUpdate, 500);
                        setTimeout(forceCanvasUpdate, 1000);
                        setTimeout(forceCanvasUpdate, 1500);

                        // Final comprehensive update to ensure everything is rendered
                        setTimeout(() => {
                            console.log('restoreBillboardDesign: Final comprehensive canvas update');

                            // Force local variable restoration one more time
                            if (window.forceRestoreLocalVariables) {
                                window.forceRestoreLocalVariables(designData);
                            }

                            // Force canvas update
                            forceCanvasUpdate();

                            // Comprehensive restoration verification
                            const canvas = document.getElementById('editorCanvas');
                            if (canvas) {
                                const ctx = canvas.getContext('2d');
                                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                                const isBlank = imageData.data.every((value, index) => index % 4 === 3 || value === 0);

                                // Verify text restoration
                                const textRestored = ['editorLine1', 'editorLine2', 'editorLine3', 'editorLine4'].some(lineId => {
                                    const input = document.getElementById(lineId);
                                    return input && input.value && input.value.trim();
                                });

                                // Verify image restoration
                                const imageRestored = window.editorState && window.editorState.editorUploadedImage;
                                const uploadContainerUpdated = document.getElementById('editorPhotoUpload') &&
                                                            document.getElementById('editorPhotoUpload').style.border.includes('28a745');

                                console.log('restoreBillboardDesign: Restoration verification:', {
                                    canvasBlank: isBlank,
                                    textRestored: textRestored,
                                    imageRestored: !!imageRestored,
                                    uploadContainerUpdated: uploadContainerUpdated,
                                    hasUploadedImageData: !!designData.uploadedImageData
                                });

                                if (isBlank) {
                                    console.warn('restoreBillboardDesign: Canvas appears blank, attempting emergency restoration');
                                    // Emergency restoration attempt
                                    setTimeout(() => {
                                        if (window.updateEditorCanvasGlobal) {
                                            window.updateEditorCanvasGlobal();
                                        }

                                        // If still blank after emergency restoration, try direct drawing
                                        setTimeout(() => {
                                            const canvas2 = document.getElementById('editorCanvas');
                                            if (canvas2) {
                                                const ctx2 = canvas2.getContext('2d');
                                                const imageData2 = ctx2.getImageData(0, 0, canvas2.width, canvas2.height);
                                                const stillBlank = imageData2.data.every((value, index) => index % 4 === 3 || value === 0);

                                                if (stillBlank) {
                                                    console.warn('restoreBillboardDesign: Emergency restoration failed, attempting direct drawing');
                                                    drawEditorTextDirect(ctx2, canvas2);
                                                    if (window.editorState.editorUploadedImage) {
                                                        const template = window.editorState.anniversaryTemplates &&
                                                                    window.editorState.anniversaryTemplates[window.editorState.currentAnniversaryTemplate];
                                                        drawEditorImageDirect(ctx2, canvas2, template);
                                                    }
                                                }
                                            }
                                        }, 300);
                                    }, 200);
                                } else {
                                    console.log('restoreBillboardDesign: Canvas restoration successful - content detected');

                                    // Additional verification for image restoration
                                    if (designData.uploadedImageData && !imageRestored) {
                                        console.warn('restoreBillboardDesign: Image data exists but image not restored, attempting image restoration');
                                        // Retry image restoration
                                        const img = new Image();
                                        img.crossOrigin = 'anonymous';
                                        img.onload = function() {
                                            window.editorState.editorUploadedImage = img;
                                            if (window.syncEditorState) {
                                                window.syncEditorState();
                                            }
                                            if (window.updateEditorCanvasGlobal) {
                                                window.updateEditorCanvasGlobal();
                                            }
                                            console.log('restoreBillboardDesign: Image restoration retry completed');
                                        };
                                        img.src = designData.uploadedImageData;
                                    }
                                }
                            }
                        }, 2000);
                    }
                }

                console.log('restoreBillboardDesign: State restoration completed successfully');
                return true;

            } catch (error) {
                console.error('restoreBillboardDesign: Error during restoration:', error);
                return false;
            }
        };

        // Helper function to draw editor text on canvas
        function drawEditorText(ctx, canvas) {
            if (!window.editorState || !window.editorState.editorTextSettings) return;

            const textSettings = window.editorState.editorTextSettings;
            const lineHeight = 60;
            const startY = 100;

            ['editorLine1', 'editorLine2', 'editorLine3', 'editorLine4'].forEach((lineId, index) => {
                const input = document.getElementById(lineId);
                const settings = textSettings[lineId];

                if (input && input.value && settings) {
                    const text = input.value;
                    const y = startY + (index * lineHeight);

                    // Set font
                    ctx.font = `${settings.fontSize}px ${settings.fontFamily}`;
                    ctx.textAlign = 'center';

                    // Draw shadow if enabled
                    if (settings.shadow) {
                        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
                        ctx.fillText(text, canvas.width / 2 + settings.shadowWidth, y + settings.shadowWidth);
                    }

                    // Draw main text
                    ctx.fillStyle = settings.color;
                    ctx.fillText(text, canvas.width / 2, y);
                }
            });

            // Draw uploaded image if available
            if (window.editorState.editorUploadedImage) {
                const img = window.editorState.editorUploadedImage;
                const imgWidth = 200;
                const imgHeight = 150;
                const imgX = canvas.width - imgWidth - 20;
                const imgY = canvas.height - imgHeight - 20;

                ctx.drawImage(img, imgX, imgY, imgWidth, imgHeight);
            }
        }

        // Direct text drawing function for fallback canvas updates
        function drawEditorTextDirect(ctx, canvas) {
            if (!window.editorState || !window.editorState.editorTextSettings) {
                console.log('drawEditorTextDirect: No editor state or text settings available');
                return;
            }

            const textSettings = window.editorState.editorTextSettings;
            console.log('drawEditorTextDirect: Drawing text with settings:', textSettings);

            ['editorLine1', 'editorLine2', 'editorLine3', 'editorLine4'].forEach((lineId, index) => {
                const input = document.getElementById(lineId);
                const settings = textSettings[lineId];

                if (input && input.value && input.value.trim() && settings) {
                    const text = input.value.trim();

                    // Use template positioning if available, otherwise use simple positioning
                    const template = window.editorState.anniversaryTemplates &&
                                window.editorState.anniversaryTemplates[window.editorState.currentAnniversaryTemplate];

                    let x, y;
                    if (template && template.textPositions && template.textPositions[index]) {
                        const pos = template.textPositions[index];
                        const scaleX = canvas.width / 800;
                        const scaleY = canvas.height / 400;
                        x = pos.x * scaleX;
                        y = pos.y * scaleY;
                        ctx.textAlign = pos.align || 'center';
                    } else {
                        // Fallback positioning
                        x = canvas.width / 2;
                        y = 100 + (index * 60);
                        ctx.textAlign = 'center';
                    }

                    // Scale font size
                    const scaledFontSize = settings.fontSize * (canvas.width / 800);
                    ctx.font = `${scaledFontSize}px ${settings.fontFamily}`;

                    // Draw shadow if enabled
                    if (settings.shadow) {
                        const shadowWidth = settings.shadowWidth || 4;
                        const scaledShadowWidth = shadowWidth * (canvas.width / 800);
                        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                        ctx.fillText(text, x + scaledShadowWidth, y + scaledShadowWidth);
                    }

                    // Draw main text
                    ctx.fillStyle = settings.color;
                    ctx.fillText(text, x, y);

                    console.log(`drawEditorTextDirect: Drew text "${text}" at (${x}, ${y}) with color ${settings.color}`);
                }
            });
        }

        // Direct image drawing function for fallback canvas updates
        function drawEditorImageDirect(ctx, canvas, template) {
            if (!window.editorState.editorUploadedImage) {
                console.log('drawEditorImageDirect: No uploaded image available');
                return;
            }

            const img = window.editorState.editorUploadedImage;
            console.log('drawEditorImageDirect: Drawing uploaded image');

            if (template && template.imagePosition) {
                // Use template positioning
                const pos = template.imagePosition;
                const scaleX = canvas.width / 800;
                const scaleY = canvas.height / 400;
                const x = pos.x * scaleX;
                const y = pos.y * scaleY;
                const width = pos.width * scaleX;
                const height = pos.height * scaleY;

                if (template.type === 'centered-image') {
                    // Draw circular image
                    ctx.save();
                    ctx.beginPath();
                    ctx.arc(x + width/2, y + height/2, width/2, 0, 2 * Math.PI);
                    ctx.clip();
                    ctx.drawImage(img, x, y, width, height);
                    ctx.restore();

                    // Draw white border
                    ctx.beginPath();
                    ctx.arc(x + width/2, y + height/2, width/2, 0, 2 * Math.PI);
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 6 * (canvas.width / 800);
                    ctx.stroke();
                } else {
                    // Draw rectangular image
                    ctx.drawImage(img, x, y, width, height);
                }

                console.log(`drawEditorImageDirect: Drew image at (${x}, ${y}) with size ${width}x${height}`);
            } else {
                // Fallback positioning - bottom right corner
                const imgWidth = 200 * (canvas.width / 800);
                const imgHeight = 150 * (canvas.height / 400);
                const imgX = canvas.width - imgWidth - 20;
                const imgY = canvas.height - imgHeight - 20;

                ctx.drawImage(img, imgX, imgY, imgWidth, imgHeight);
                console.log(`drawEditorImageDirect: Drew image at fallback position (${imgX}, ${imgY})`);
            }
        }

        // Professional comprehensive UI restoration function
        window.restoreCompleteUIState = function() {
            console.log('restoreCompleteUIState: Starting comprehensive UI restoration...');

            try {
                const savedData = localStorage.getItem('adDesignData');
                if (!savedData) {
                    console.log('restoreCompleteUIState: No saved design data found');
                    return false;
                }

                const designData = JSON.parse(savedData);
                console.log('restoreCompleteUIState: Restoring UI from data:', designData);

                // Restore background graphic selection visual state
                if (designData.bgGraphic) {
                    const backgroundOptions = document.querySelectorAll('.anniversary-bg-option');
                    backgroundOptions.forEach(option => {
                        option.classList.remove('selected');
                        if (option.dataset.background === designData.bgGraphic) {
                            option.classList.add('selected');
                            console.log(`restoreCompleteUIState: Selected background graphic ${designData.bgGraphic}`);
                        }
                    });
                }

                // Restore background type selection
                if (designData.bgType) {
                    const typeButtons = document.querySelectorAll('.background-type-btn');
                    typeButtons.forEach(btn => {
                        btn.classList.remove('active');
                        if (btn.dataset.type === designData.bgType) {
                            btn.classList.add('active');
                            console.log(`restoreCompleteUIState: Selected background type ${designData.bgType}`);
                        }
                    });

                    // Show/hide appropriate background sections
                    const imageSection = document.getElementById('imageBackgroundSection');
                    const colorSection = document.getElementById('colorBackgroundSection');
                    if (imageSection && colorSection) {
                        if (designData.bgType === 'image') {
                            imageSection.style.display = 'block';
                            colorSection.style.display = 'none';
                        } else {
                            imageSection.style.display = 'none';
                            colorSection.style.display = 'block';
                        }
                    }
                }

                // Restore anniversary template selection visual state
                if (designData.template) {
                    const templateCards = document.querySelectorAll('.anniversary-template-card');
                    templateCards.forEach(card => {
                        card.classList.remove('selected');
                        if (card.dataset.template === designData.template) {
                            card.classList.add('selected');
                            console.log(`restoreCompleteUIState: Selected template ${designData.template}`);
                        }
                    });
                }

                // Restore color picker visual states
                if (designData.uiState && designData.uiState.colorPickerStates) {
                    Object.keys(designData.uiState.colorPickerStates).forEach(btnId => {
                        const btn = document.getElementById(btnId);
                        if (btn) {
                            btn.style.backgroundColor = designData.uiState.colorPickerStates[btnId];
                            console.log(`restoreCompleteUIState: Restored color picker ${btnId} visual state`);
                        }
                    });
                }

                // Restore font settings to UI elements (if font modal exists)
                if (designData.textSettings) {
                    // Store text settings for font modal restoration
                    localStorage.setItem('editorTextSettings', JSON.stringify(designData.textSettings));

                    // Apply text settings to color picker buttons immediately
                    Object.keys(designData.textSettings).forEach(lineId => {
                        const settings = designData.textSettings[lineId];
                        const lineNum = lineId.replace('editorLine', '');
                        const colorBtn = document.getElementById(`editorColorBtn${lineNum}`);
                        if (colorBtn && settings.color) {
                            colorBtn.style.backgroundColor = settings.color;
                            console.log(`restoreCompleteUIState: Applied text color ${settings.color} to ${lineId}`);
                        }
                    });

                    console.log('restoreCompleteUIState: Text settings restored and applied to UI');
                }

                // Restore uploaded image visual indicator
                if (designData.uploadedImageData) {
                    const uploadContainer = document.getElementById('editorPhotoUpload');
                    if (uploadContainer) {
                        // Update upload container to show image is uploaded
                        uploadContainer.style.border = '2px solid #28a745';
                        uploadContainer.style.backgroundColor = '#d4edda';
                        const uploadText = uploadContainer.querySelector('.upload-text');
                        if (uploadText) {
                            uploadText.textContent = 'Photo uploaded ✓ Click to change';
                            uploadText.style.color = '#155724';
                        }
                        console.log('restoreCompleteUIState: Upload container updated to show uploaded image');
                    }
                }

                // Ensure editor container is visible and active
                const editorContainer = document.getElementById('templateEditorContainer');
                if (editorContainer) {
                    editorContainer.classList.add('active');
                    console.log('restoreCompleteUIState: Editor container ensured to be active');
                }

                // Restore global editor state
                if (window.editorState) {
                    window.editorState.currentAnniversaryTemplate = designData.template;
                    window.editorState.selectedAnniversaryBackground = designData.bgGraphic || 'Anniversary-1';
                    window.editorState.selectedBackgroundType = designData.bgType || 'image';
                    window.editorState.selectedBackgroundColor = designData.bgColor || '#ff0000';
                    window.editorState.isEditorMode = true;

                    if (designData.textSettings) {
                        window.editorState.editorTextSettings = designData.textSettings;
                    }

                    console.log('restoreCompleteUIState: Global editor state synchronized');
                }

                console.log('restoreCompleteUIState: Comprehensive UI restoration completed successfully');
                return true;

            } catch (error) {
                console.error('restoreCompleteUIState: Error during UI restoration:', error);
                return false;
            }
        };
        // Communication with Dropdown Menu form
        window.addEventListener('message', function(event) {
            if (event.origin !== window.location.origin) return;

            if (event.data.type === 'purposeSelected' && event.data.purpose) {
                console.log('Purpose selected via message:', event.data.purpose);
                handlePurposeChange(event.data.purpose);
            }
        });

        document.addEventListener('purposeSelected', function(event) {
            if (event.detail && event.detail.purpose) {
                console.log('Purpose selected via event:', event.detail.purpose);
                handlePurposeChange(event.detail.purpose);
            }
        });

        // Also listen for storage changes (in case purpose is set from another tab/window)
        window.addEventListener('storage', function(event) {
            if (event.key === 'selectedPurpose' && event.newValue) {
                console.log('Purpose selected via storage:', event.newValue);
                handlePurposeChange(event.newValue);
            }
        });

        // Handle purpose change with proper cleanup
        function handlePurposeChange(newPurpose) {
            // Close any open editor
            const editorContainer = document.getElementById('templateEditorContainer');
            if (editorContainer && editorContainer.classList.contains('active')) {
                backToTemplateSelection();
            }

            // Close any open modals
            const fontModal = document.getElementById('editorFontModal');
            if (fontModal && fontModal.style.display === 'flex') {
                window.closeEditorFontModal();
            }

            const bgDialog = document.getElementById('backgroundGraphicDialog');
            if (bgDialog && bgDialog.classList.contains('show')) {
                bgDialog.classList.remove('show');
            }

            // Reset template selections
            currentAnniversaryTemplate = null;
            currentBenefitTemplate = null;
            editorUploadedImage = null;
            syncEditorState();

            // Update purpose
            currentPurpose = newPurpose;
            localStorage.setItem('selectedPurpose', newPurpose);
            updatePurposeField(newPurpose);
            displayPurpose(newPurpose);
        }

        // Template Variables - Make them globally accessible
        window.editorState = {
            currentAnniversaryTemplate: null,
            selectedAnniversaryBackground: 'Anniversary-1',
            currentBenefitTemplate: null,
            selectedBenefitBackground: 'Benefit-3',
            selectedBackgroundType: 'image',
            selectedBackgroundColor: '#ff0000',
            editorCanvas: null,
            editorCtx: null,
            editorUploadedImage: null,
            isEditorMode: false,
            anniversaryTemplates: anniversaryTemplates, // Pass the templates
            benefitTemplates: benefitTemplates, // Pass the benefit templates
            editorTextSettings: null
        };

        // Initialize global debugging
        window.debugEditorState = function() {
            console.log('Current Editor State:', window.editorState);
            console.log('Local variables:', {
                currentAnniversaryTemplate,
                selectedAnniversaryBackground,
                selectedBackgroundType,
                selectedBackgroundColor,
                isEditorMode
            });
        };

        // Helper function to sync global state - make it globally accessible
        function syncEditorState() {
            // Ensure canvas elements are initialized
            if (!editorCanvas) {
                editorCanvas = document.getElementById('editorCanvas');
                if (editorCanvas) {
                    editorCtx = editorCanvas.getContext('2d');
                }
            }

            window.editorState.currentAnniversaryTemplate = currentAnniversaryTemplate;
            window.editorState.selectedAnniversaryBackground = selectedAnniversaryBackground;
            window.editorState.currentBenefitTemplate = currentBenefitTemplate;
            window.editorState.selectedBenefitBackground = selectedBenefitBackground;
            window.editorState.selectedBackgroundType = selectedBackgroundType;
            window.editorState.selectedBackgroundColor = selectedBackgroundColor;
            window.editorState.editorCanvas = editorCanvas;
            window.editorState.editorCtx = editorCtx;
            window.editorState.editorUploadedImage = editorUploadedImage || window.editorState.editorUploadedImage;
            window.editorState.isEditorMode = isEditorMode;
            window.editorState.anniversaryTemplates = anniversaryTemplates;
            window.editorState.benefitTemplates = benefitTemplates;
            window.editorState.editorTextSettings = editorTextSettings;

            // Also sync back from global to local to ensure consistency
            editorUploadedImage = window.editorState.editorUploadedImage;

            console.log('syncEditorState: State synchronized, canvas available:', !!editorCanvas);
        }

        // Make syncEditorState globally accessible
        window.syncEditorState = syncEditorState;

        // Global function to force restore local variables from saved data
        window.forceRestoreLocalVariables = function(designData) {
            console.log('forceRestoreLocalVariables: Restoring local variables from design data');

            if (designData.template) {
                currentAnniversaryTemplate = designData.template;
                console.log('forceRestoreLocalVariables: Set currentAnniversaryTemplate =', currentAnniversaryTemplate);
            }

            if (designData.bgGraphic) {
                selectedAnniversaryBackground = designData.bgGraphic;
                console.log('forceRestoreLocalVariables: Set selectedAnniversaryBackground =', selectedAnniversaryBackground);
            }

            if (designData.bgType) {
                selectedBackgroundType = designData.bgType;
                console.log('forceRestoreLocalVariables: Set selectedBackgroundType =', selectedBackgroundType);
            }

            if (designData.bgColor) {
                selectedBackgroundColor = designData.bgColor;
                console.log('forceRestoreLocalVariables: Set selectedBackgroundColor =', selectedBackgroundColor);
            }

            isEditorMode = true;

            // Restore text settings
            if (designData.textSettings) {
                Object.keys(designData.textSettings).forEach(lineId => {
                    if (editorTextSettings[lineId]) {
                        editorTextSettings[lineId] = { ...editorTextSettings[lineId], ...designData.textSettings[lineId] };
                    }
                });
                console.log('forceRestoreLocalVariables: Text settings restored');
            }

            // Restore uploaded image to local variable
            if (designData.uploadedImageData && window.editorState.editorUploadedImage) {
                editorUploadedImage = window.editorState.editorUploadedImage;
                console.log('forceRestoreLocalVariables: Uploaded image restored to local variable');
            }

            // Ensure canvas is initialized
            if (!editorCanvas) {
                editorCanvas = document.getElementById('editorCanvas');
                if (editorCanvas) {
                    editorCtx = editorCanvas.getContext('2d');
                    console.log('forceRestoreLocalVariables: Canvas initialized');
                }
            }

            // Sync to global state
            syncEditorState();

            console.log('forceRestoreLocalVariables: Local variables restoration completed');
        };

        // Local variables for easier access within this scope
        let currentAnniversaryTemplate = null;
        let selectedAnniversaryBackground = 'Anniversary-1';
        let currentBenefitTemplate = null;
        let selectedBenefitBackground = 'Benefit-3';
        let selectedBackgroundType = 'image'; // 'image' or 'color'
        let selectedBackgroundColor = '#ff0000';
        let editorCanvas = null;
        let editorCtx = null;
        let editorUploadedImage = null;
        let isEditorMode = false;

        // Anniversary Template Text Settings
        const editorTextSettings = {
            editorLine1: { color: '#ffffff', fontSize: 50, fontFamily: 'Arial', shadow: true, shadowWidth: 4 },
            editorLine2: { color: '#ffffff', fontSize: 50, fontFamily: 'Arial', shadow: true, shadowWidth: 4 },
            editorLine3: { color: '#ffffff', fontSize: 50, fontFamily: 'Arial', shadow: true, shadowWidth: 4 },
            editorLine4: { color: '#ffffff', fontSize: 50, fontFamily: 'Arial', shadow: true, shadowWidth: 4 }
        };

        // Initial sync to set up global state
        syncEditorState();

        // Initialize Anniversary Templates
        function initAnniversaryTemplates() {
            const templateCards = document.querySelectorAll('.anniversary-template-card');
            templateCards.forEach(card => {
                card.addEventListener('click', function() {
                    const templateId = this.dataset.template;
                    selectAnniversaryTemplate(templateId);
                });
            });

            // Initialize editor canvas
            editorCanvas = document.getElementById('editorCanvas');
            if (editorCanvas) {
                editorCtx = editorCanvas.getContext('2d');
            }
            syncEditorState();

            // Background graphic button
            const bgGraphicBtn = document.getElementById('backgroundGraphicBtn');
            if (bgGraphicBtn) {
                bgGraphicBtn.addEventListener('click', showBackgroundGraphicDialog);
            }

            // Editor photo upload
            const editorPhotoUpload = document.getElementById('editorPhotoUpload');
            const editorPhotoInput = document.getElementById('editorPhotoInput');
            if (editorPhotoUpload && editorPhotoInput) {
                editorPhotoUpload.addEventListener('click', () => editorPhotoInput.click());
                editorPhotoInput.addEventListener('change', handleEditorPhotoUpload);
            }

            // Editor text inputs with validation
            const editorTextInputs = ['editorLine1', 'editorLine2', 'editorLine3', 'editorLine4'];
            editorTextInputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.addEventListener('input', function() {
                        validateTextInput(this);
                        updateEditorCanvas();
                    });
                    // Initialize character counter
                    validateTextInput(input);
                }
            });

            // Note: Back to templates button and Confirm template button now use onclick attributes
            // No need for addEventListener to prevent duplicate calls
            console.log('Anniversary template buttons configured with onclick attributes');

            // Initialize anniversary backgrounds
            initAnniversaryBackgrounds();

            // Setup editor color pickers and settings buttons
            setupEditorColorPickers();
            setupEditorSettingsButtons();

            // Add resize listener for responsive canvas
            window.addEventListener('resize', function() {
                if (isEditorMode && editorCanvas) {
                    setTimeout(updateEditorCanvas, 100);
                }
            });

            // Setup image resize modal buttons
            const cancelResizeBtn = document.getElementById('cancelResizeBtn');
            const confirmResizeBtn = document.getElementById('confirmResizeBtn');

            if (cancelResizeBtn) {
                cancelResizeBtn.addEventListener('click', function() {
                    cancelImageResize();
                });
            }

            if (confirmResizeBtn) {
                confirmResizeBtn.addEventListener('click', function() {
                    confirmImageResize();
                });
            }
        }

        // Initialize Benefit Templates
        function initBenefitTemplates() {
            const templateCards = document.querySelectorAll('.benefit-template-card');
            templateCards.forEach(card => {
                card.addEventListener('click', function() {
                    const templateId = this.dataset.template;
                    selectBenefitTemplate(templateId);
                });
            });

            // Initialize editor canvas
            editorCanvas = document.getElementById('editorCanvas');
            if (editorCanvas) {
                editorCtx = editorCanvas.getContext('2d');
            }
            syncEditorState();

            // Background graphic button
            const bgGraphicBtn = document.getElementById('backgroundGraphicBtn');
            if (bgGraphicBtn) {
                bgGraphicBtn.addEventListener('click', showBackgroundGraphicDialog);
            }

            // Editor photo upload
            const editorPhotoUpload = document.getElementById('editorPhotoUpload');
            const editorPhotoInput = document.getElementById('editorPhotoInput');
            if (editorPhotoUpload && editorPhotoInput) {
                editorPhotoUpload.addEventListener('click', () => editorPhotoInput.click());
                editorPhotoInput.addEventListener('change', handleEditorPhotoUpload);
            }

            // Editor text inputs with validation
            const editorTextInputs = ['editorLine1', 'editorLine2', 'editorLine3', 'editorLine4'];
            editorTextInputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.addEventListener('input', function() {
                        validateTextInput(this);
                        updateEditorCanvas();
                    });
                    // Initialize character counter
                    validateTextInput(input);
                }
            });

            // Note: Back to templates button and Confirm template button now use onclick attributes
            // No need for addEventListener to prevent duplicate calls
            console.log('Benefit template buttons configured with onclick attributes');

            // Initialize benefit backgrounds
            initBenefitBackgrounds();

            // Setup editor color pickers and settings buttons
            setupEditorColorPickers();
            setupEditorSettingsButtons();

            // Add resize listener for responsive canvas
            window.addEventListener('resize', function() {
                if (isEditorMode && editorCanvas) {
                    setTimeout(updateEditorCanvas, 100);
                }
            });

            // Setup image resize modal buttons
            const cancelResizeBtn = document.getElementById('cancelResizeBtn');
            const confirmResizeBtn = document.getElementById('confirmResizeBtn');

            if (cancelResizeBtn) {
                cancelResizeBtn.addEventListener('click', function() {
                    cancelImageResize();
                });
            }

            if (confirmResizeBtn) {
                confirmResizeBtn.addEventListener('click', function() {
                    confirmImageResize();
                });
            }
        }

        // Select Anniversary Template
        function selectAnniversaryTemplate(templateId) {
            // Remove previous selection
            document.querySelectorAll('.anniversary-template-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Select current template
            const selectedCard = document.querySelector(`[data-template="${templateId}"]`);
            if (selectedCard) {
                selectedCard.classList.add('selected');
            }

            currentAnniversaryTemplate = templateId;

            // Set default background based on template
            switch(templateId) {
                case 'anniversary-template-1':
                    selectedAnniversaryBackground = 'Anniversary-1';
                    break;
                case 'anniversary-template-2':
                    selectedAnniversaryBackground = 'Anniversary-2';
                    break;
                case 'anniversary-template-3':
                    selectedAnniversaryBackground = 'Anniversary-3';
                    break;
                default:
                    selectedAnniversaryBackground = 'Anniversary-1';
            }

            syncEditorState();

            // Populate text fields with default values and initialize editor text settings
            const template = anniversaryTemplates[templateId];
            if (template && template.defaultTexts) {
                template.defaultTexts.forEach((text, index) => {
                    const input = document.getElementById(`editorLine${index + 1}`);
                    if (input) {
                        input.value = text;
                        input.placeholder = text;
                    }
                });
            }

            // Initialize editor text settings with template textStyles as defaults
            if (template && template.textStyles) {
                template.textStyles.forEach((style, index) => {
                    const lineId = `editorLine${index + 1}`;
                    if (window.editorState && window.editorState.editorTextSettings) {
                        window.editorState.editorTextSettings[lineId] = {
                            color: style.color || '#ffffff',
                            fontSize: parseInt(style.fontSize) || 42,
                            fontFamily: style.fontFamily || 'Arial',
                            shadow: style.textShadow ? true : false,
                            shadowWidth: 4,
                            fontWeight: style.fontWeight || 'normal',
                            fontStyle: style.fontStyle || 'normal',
                            letterSpacing: style.letterSpacing || 'normal'
                        };
                    }

                    // Update color button to reflect template color
                    const colorBtn = document.getElementById(`editorColorBtn${index + 1}`);
                    if (colorBtn && style.color) {
                        colorBtn.style.backgroundColor = style.color;
                    }
                });
            }

            // Update background selection in dialog
            updateBackgroundSelection();

            // Show editor
            showTemplateEditor();
        }

        // Select Benefit Template
        function selectBenefitTemplate(templateId) {
            // Remove previous selection
            document.querySelectorAll('.benefit-template-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Select current template
            const selectedCard = document.querySelector(`[data-template="${templateId}"]`);
            if (selectedCard) {
                selectedCard.classList.add('selected');
            }

            currentBenefitTemplate = templateId;

            // Clear any uploaded image when selecting a new template to show default image
            editorUploadedImage = null;
            if (window.editorState) {
                window.editorState.editorUploadedImage = null;
            }

            // Set default background based on template
            switch(templateId) {
                case 'benefit-template-1':
                    selectedBenefitBackground = 'Benefit-3';
                    break;
                case 'benefit-template-2':
                    selectedBenefitBackground = 'Benefit-4';
                    break;
                case 'benefit-template-3':
                    selectedBenefitBackground = 'Benefit-5';
                    break;
                default:
                    selectedBenefitBackground = 'Benefit-3';
            }

            syncEditorState();

            // Populate text fields with default values and initialize editor text settings
            const template = benefitTemplates[templateId];
            if (template && template.defaultTexts) {
                template.defaultTexts.forEach((text, index) => {
                    const input = document.getElementById(`editorLine${index + 1}`);
                    if (input) {
                        input.value = text;
                        input.placeholder = text;
                    }
                });
            }

            // Initialize editor text settings with template textStyles as defaults
            if (template && template.textStyles) {
                template.textStyles.forEach((style, index) => {
                    const lineId = `editorLine${index + 1}`;
                    if (window.editorState && window.editorState.editorTextSettings) {
                        window.editorState.editorTextSettings[lineId] = {
                            color: style.color || '#ffffff',
                            fontSize: parseInt(style.fontSize) || 42,
                            fontFamily: style.fontFamily || 'Arial',
                            shadow: style.textShadow ? true : false,
                            shadowWidth: 4,
                            fontWeight: style.fontWeight || 'normal',
                            fontStyle: style.fontStyle || 'normal',
                            letterSpacing: style.letterSpacing || 'normal'
                        };
                    }

                    // Update color button to reflect template color
                    const colorBtn = document.getElementById(`editorColorBtn${index + 1}`);
                    if (colorBtn && style.color) {
                        colorBtn.style.backgroundColor = style.color;
                    }
                });
            }

            // Update background selection in dialog
            updateBackgroundSelection();

            // Show editor
            showTemplateEditor();
        }

        // Show Template Editor
        function showTemplateEditor() {
            // Find the appropriate templates container based on current purpose
            let templatesContainer = null;
            if (currentPurpose === 'Anniversary') {
                templatesContainer = document.querySelector('.anniversary-templates-container').parentElement;
            } else if (currentPurpose === 'Benefit') {
                templatesContainer = document.querySelector('.benefit-templates-container').parentElement;
            }

            const editorContainer = document.getElementById('templateEditorContainer');

            if (templatesContainer && editorContainer) {
                templatesContainer.style.display = 'none';
                editorContainer.classList.add('active');
                isEditorMode = true;
                syncEditorState();

                // Update editor title based on current purpose
                const editorTitle = editorContainer.querySelector('.design-section-title');
                if (editorTitle && currentPurpose) {
                    editorTitle.textContent = `Customize Your ${currentPurpose} Billboard`;
                }

                // Force setup editor controls to ensure they work
                forceSetupEditorColorPickers();

                // Initialize editor canvas
                setTimeout(() => {
                    updateEditorCanvas();
                    updateBackgroundSelection();
                }, 100);
            }
        }

        // Reset all form fields and state
        function resetAllFields() {
            console.log('Resetting all fields and state...');

            // Reset text input fields
            const textInputs = ['line1', 'line2', 'line3', 'line4', 'editorLine1', 'editorLine2', 'editorLine3', 'editorLine4'];
            textInputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.value = '';
                }
            });

            // Reset color buttons to default white
            const colorButtons = ['colorBtn1', 'colorBtn2', 'colorBtn3', 'colorBtn4', 'editorColorBtn1', 'editorColorBtn2', 'editorColorBtn3', 'editorColorBtn4'];
            colorButtons.forEach(btnId => {
                const btn = document.getElementById(btnId);
                if (btn) {
                    btn.style.backgroundColor = '#ffffff';
                }
            });

            // Reset text settings to defaults
            const defaultTextSettings = { color: '#ffffff', fontSize: 50, fontFamily: 'Arial', shadow: true, shadowWidth: 4 };
            if (window.textSettings) {
                Object.keys(window.textSettings).forEach(line => {
                    window.textSettings[line] = { ...defaultTextSettings };
                });
            }
            if (window.editorTextSettings) {
                Object.keys(window.editorTextSettings).forEach(line => {
                    window.editorTextSettings[line] = { ...defaultTextSettings };
                });
            }

            // Reset global editor state
            if (window.editorState) {
                window.editorState.editorTextSettings = {
                    editorLine1: { ...defaultTextSettings },
                    editorLine2: { ...defaultTextSettings },
                    editorLine3: { ...defaultTextSettings },
                    editorLine4: { ...defaultTextSettings }
                };
                window.editorState.editorUploadedImage = null;
                window.editorState.selectedBackgroundColor = '#000000';
                window.editorState.selectedAnniversaryBackground = 'Anniversary-1';
                window.editorState.selectedBackgroundType = 'image';
            }

            // Reset uploaded images
            if (window.uploadedImage) window.uploadedImage = null;
            if (window.editorUploadedImage) window.editorUploadedImage = null;

            // Reset background settings
            if (window.selectedBackgroundColor) window.selectedBackgroundColor = '#000000';
            if (window.selectedAnniversaryBackground) window.selectedAnniversaryBackground = 'Anniversary-1';
            if (window.selectedBackgroundType) window.selectedBackgroundType = 'image';

            // Clear template selections
            document.querySelectorAll('.template-card, .anniversary-template-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Reset canvas if it exists
            const canvas = document.getElementById('billboardCanvas');
            const editorCanvas = document.getElementById('editorCanvas');
            [canvas, editorCanvas].forEach(canvasEl => {
                if (canvasEl) {
                    const ctx = canvasEl.getContext('2d');
                    ctx.clearRect(0, 0, canvasEl.width, canvasEl.height);
                    ctx.fillStyle = '#000000';
                    ctx.fillRect(0, 0, canvasEl.width, canvasEl.height);
                }
            });

            // Clear localStorage related to design
            localStorage.removeItem('adDesignData');
            localStorage.removeItem('editorShouldStayOpen');
            localStorage.removeItem('returningFromCheckout');

            console.log('All fields and state reset successfully');
        }

        // Back to Template Selection with reset option
        function backToTemplateSelection(shouldReset = true) {
            if (shouldReset) {
                resetAllFields();
            }

            const editorContainer = document.getElementById('templateEditorContainer');

            if (editorContainer) {
                editorContainer.classList.remove('active');
                isEditorMode = false;
                syncEditorState();
            }

            // Show the appropriate template section based on current purpose
            if (currentPurpose) {
                showTemplatesForPurpose(currentPurpose);
            } else {
                showDefaultTemplateState();
            }
        }

        // Helper function to scale positions based on canvas size
        function getScaledPosition(originalPos, canvasWidth, canvasHeight) {
            // Original canvas design size is 800x400
            const originalWidth = 800;
            const originalHeight = 400;

            const scaleX = canvasWidth / originalWidth;
            const scaleY = canvasHeight / originalHeight;

            return {
                x: originalPos.x * scaleX,
                y: originalPos.y * scaleY,
                width: originalPos.width ? originalPos.width * scaleX : undefined,
                height: originalPos.height ? originalPos.height * scaleY : undefined,
                align: originalPos.align
            };
        }

        // Update Editor Canvas
        function updateEditorCanvas() {
            // Sync local state to global state before updating
            syncEditorState();

            // Always use the global version to ensure consistency
            if (window.updateEditorCanvasGlobal) {
                console.log('updateEditorCanvas: Delegating to global version');
                window.updateEditorCanvasGlobal();
                return;
            }

            // Fallback to local version if global not available
            if (!editorCanvas || !editorCtx || (!currentAnniversaryTemplate && !currentBenefitTemplate)) {
                console.log('updateEditorCanvas: Missing required elements');
                return;
            }

            let template = null;
            if (currentAnniversaryTemplate) {
                template = anniversaryTemplates[currentAnniversaryTemplate];
            } else if (currentBenefitTemplate) {
                template = benefitTemplates[currentBenefitTemplate];
            }

            if (!template) {
                console.log('updateEditorCanvas: Template not found');
                return;
            }

            console.log('updateEditorCanvas: Starting canvas update, uploaded image exists:', !!editorUploadedImage);

            // Set canvas size to match container
            const container = editorCanvas.parentElement;
            const containerRect = container.getBoundingClientRect();
            editorCanvas.width = containerRect.width;
            editorCanvas.height = containerRect.height;

            // Clear canvas
            editorCtx.clearRect(0, 0, editorCanvas.width, editorCanvas.height);

            // Draw background
            drawEditorBackground(template);
        }

        // Make updateEditorCanvas globally accessible with debouncing
        let updateCanvasTimeout = null;
        window.updateEditorCanvas = function() {
            // Debounce canvas updates to prevent excessive calls
            if (updateCanvasTimeout) {
                clearTimeout(updateCanvasTimeout);
            }
            updateCanvasTimeout = setTimeout(() => {
                updateEditorCanvas();
            }, 50);
        };

        // Create a global version that uses global state with debouncing
        window.updateEditorCanvasGlobal = function() {
            const state = window.editorState;
            if (!state || !state.editorCanvas || !state.editorCtx || (!state.currentAnniversaryTemplate && !state.currentBenefitTemplate)) {
                console.log('updateEditorCanvasGlobal: Missing required elements in global state');
                return;
            }

            let template = null;
            if (state.currentAnniversaryTemplate) {
                template = state.anniversaryTemplates[state.currentAnniversaryTemplate];
            } else if (state.currentBenefitTemplate) {
                template = state.benefitTemplates[state.currentBenefitTemplate];
            }

            if (!template) {
                console.log('updateEditorCanvasGlobal: Template not found in global state');
                return;
            }

            console.log('updateEditorCanvasGlobal: Starting canvas update, uploaded image exists:', !!state.editorUploadedImage);

            // Set canvas size to match container
            const container = state.editorCanvas.parentElement;
            const containerRect = container.getBoundingClientRect();
            state.editorCanvas.width = containerRect.width;
            state.editorCanvas.height = containerRect.height;

            // Clear canvas
            state.editorCtx.clearRect(0, 0, state.editorCanvas.width, state.editorCanvas.height);

            // Draw background using global state
            drawEditorBackgroundGlobal(template, state);
        };

        // Global version of drawEditorBackground
        function drawEditorBackgroundGlobal(template, state) {
            // Check if template has a color background (like benefit-template-2)
            if (template && template.background && template.background.startsWith('#')) {
                // Draw template-defined color background
                state.editorCtx.fillStyle = template.background;
                state.editorCtx.fillRect(0, 0, state.editorCanvas.width, state.editorCanvas.height);
                console.log('Template color background drawn (global):', template.background);

                // Draw text and image after background
                setTimeout(() => {
                    drawEditorTextGlobal(template, state);
                    drawEditorImageGlobal(template, state);
                    console.log('Text and image drawing completed (global)');
                }, 20);
            } else if (state.selectedBackgroundType === 'color') {
                // Draw user-selected solid color background
                state.editorCtx.fillStyle = state.selectedBackgroundColor;
                state.editorCtx.fillRect(0, 0, state.editorCanvas.width, state.editorCanvas.height);
                console.log('Color background drawn (global):', state.selectedBackgroundColor);

                // Draw text and image after background
                setTimeout(() => {
                    drawEditorTextGlobal(template, state);
                    drawEditorImageGlobal(template, state);
                    console.log('Text and image drawing completed (global)');
                }, 20);
            } else {
                // Draw image background - determine which background to use based on current template
                let backgroundName = null;
                if (state.currentAnniversaryTemplate) {
                    backgroundName = state.selectedAnniversaryBackground;
                } else if (state.currentBenefitTemplate) {
                    backgroundName = state.selectedBenefitBackground;
                }

                if (!backgroundName) {
                    console.error('No background name found in global state');
                    return;
                }

                const backgroundUrl = `https://www.borgesmedia.com/wp-content/uploads/2025/06/${backgroundName}.png`;
                const img = new Image();
                img.crossOrigin = 'anonymous';
                img.onload = function() {
                    state.editorCtx.drawImage(img, 0, 0, state.editorCanvas.width, state.editorCanvas.height);
                    console.log('Background image loaded and drawn (global)');

                    // Draw text and image after background loads
                    setTimeout(() => {
                        drawEditorTextGlobal(template, state);
                        drawEditorImageGlobal(template, state);
                        console.log('Text and image drawing completed (global)');
                    }, 20);
                };
                img.onerror = function() {
                    console.error('Failed to load background image (global):', backgroundUrl);
                    // Still try to draw text and image even if background fails
                    setTimeout(() => {
                        drawEditorTextGlobal(template, state);
                        drawEditorImageGlobal(template, state);
                    }, 20);
                };
                img.src = backgroundUrl;
            }
        }

        // Global version of drawEditorImage
        function drawEditorImageGlobal(template, state) {
            // Skip image drawing for text-only templates
            if (!template.imagePosition || template.type === 'text-only') return;

            console.log('Drawing editor image (global), editorUploadedImage exists:', !!state.editorUploadedImage);

            const originalPos = template.imagePosition;
            const scaledPos = getScaledPositionGlobal(originalPos, state.editorCanvas.width, state.editorCanvas.height);

            if (template.type === 'full-image') {
                // Template 1: Full-size image
                const imageToUse = state.editorUploadedImage || template.defaultImage;
                if (imageToUse) {
                    if (typeof imageToUse === 'string') {
                        // Load default image
                        const img = new Image();
                        img.crossOrigin = 'anonymous';
                        img.onload = function() {
                            state.editorCtx.drawImage(img, scaledPos.x, scaledPos.y, scaledPos.width, scaledPos.height);
                            console.log('Default image drawn for full-image template (global)');
                        };
                        img.src = imageToUse;
                    } else {
                        // Draw uploaded image
                        try {
                            state.editorCtx.drawImage(imageToUse, scaledPos.x, scaledPos.y, scaledPos.width, scaledPos.height);
                            console.log('Uploaded image drawn for full-image template (global)');
                        } catch (error) {
                            console.error('Error drawing uploaded image (global):', error);
                        }
                    }
                }
            } else if (template.type === 'placeholder-space') {
                // Template 2: Placeholder space
                if (state.editorUploadedImage) {
                    try {
                        state.editorCtx.drawImage(state.editorUploadedImage, scaledPos.x, scaledPos.y, scaledPos.width, scaledPos.height);
                        console.log('Uploaded image drawn for placeholder-space template (global)');
                    } catch (error) {
                        console.error('Error drawing uploaded image for placeholder (global):', error);
                    }
                } else {
                    // Draw placeholder with scaled dimensions
                    state.editorCtx.save();
                    state.editorCtx.strokeStyle = '#ddd';
                    const scaledDash = 10 * (state.editorCanvas.width / 800);
                    state.editorCtx.setLineDash([scaledDash, scaledDash / 2]);
                    state.editorCtx.lineWidth = 2 * (state.editorCanvas.width / 800);
                    state.editorCtx.strokeRect(scaledPos.x, scaledPos.y, scaledPos.width, scaledPos.height);

                    // Placeholder text with scaled font
                    state.editorCtx.fillStyle = '#666';
                    const scaledFontSize = 16 * (state.editorCanvas.width / 800);
                    state.editorCtx.font = `${scaledFontSize}px Arial`;
                    state.editorCtx.textAlign = 'center';
                    //state.editorCtx.fillText(template.placeholderText, scaledPos.x + scaledPos.width/2, scaledPos.y + scaledPos.height/2);
                    state.editorCtx.restore();
                    console.log('Placeholder drawn for placeholder-space template (global)');
                }
            } else if (template.type === 'centered-image') {
                // Template 3: Centered smaller image
                const imageToUse = state.editorUploadedImage || template.defaultImage;
                if (imageToUse) {
                    if (typeof imageToUse === 'string') {
                        // Load default image
                        const img = new Image();
                        img.crossOrigin = 'anonymous';
                        img.onload = function() {
                            // Draw circular image with scaled dimensions
                            state.editorCtx.save();
                            state.editorCtx.beginPath();
                            state.editorCtx.arc(scaledPos.x + scaledPos.width/2, scaledPos.y + scaledPos.height/2, scaledPos.width/2, 0, 2 * Math.PI);
                            state.editorCtx.clip();
                            state.editorCtx.drawImage(img, scaledPos.x, scaledPos.y, scaledPos.width, scaledPos.height);
                            state.editorCtx.restore();

                            // Draw white border with scaled width
                            state.editorCtx.beginPath();
                            state.editorCtx.arc(scaledPos.x + scaledPos.width/2, scaledPos.y + scaledPos.height/2, scaledPos.width/2, 0, 2 * Math.PI);
                            state.editorCtx.strokeStyle = 'white';
                            state.editorCtx.lineWidth = 6 * (state.editorCanvas.width / 800);
                            state.editorCtx.stroke();
                            console.log('Default image drawn for centered-image template (global)');
                        };
                        img.src = imageToUse;
                    } else {
                        // Draw uploaded image (circular) with scaled dimensions
                        try {
                            state.editorCtx.save();
                            state.editorCtx.beginPath();
                            state.editorCtx.arc(scaledPos.x + scaledPos.width/2, scaledPos.y + scaledPos.height/2, scaledPos.width/2, 0, 2 * Math.PI);
                            state.editorCtx.clip();
                            state.editorCtx.drawImage(state.editorUploadedImage, scaledPos.x, scaledPos.y, scaledPos.width, scaledPos.height);
                            state.editorCtx.restore();

                            // Draw white border with scaled width
                            state.editorCtx.beginPath();
                            state.editorCtx.arc(scaledPos.x + scaledPos.width/2, scaledPos.y + scaledPos.height/2, scaledPos.width/2, 0, 2 * Math.PI);
                            state.editorCtx.strokeStyle = 'white';
                            state.editorCtx.lineWidth = 6 * (state.editorCanvas.width / 800);
                            state.editorCtx.stroke();
                            console.log('Uploaded image drawn for centered-image template (global)');
                        } catch (error) {
                            console.error('Error drawing uploaded image for centered template (global):', error);
                        }
                    }
                }
            }
        }

        // Global version of drawEditorText
        function drawEditorTextGlobal(template, state) {
            const textInputs = ['editorLine1', 'editorLine2', 'editorLine3', 'editorLine4'];

            textInputs.forEach((lineId, index) => {
                const input = document.getElementById(lineId);
                const text = input ? input.value : '';

                if (text.trim()) {
                    // Use editor settings (which may have been initialized from template styles)
                    let settings = state.editorTextSettings[lineId];

                    const originalPosition = template.textPositions[index];
                    const scaledPosition = getScaledPositionGlobal(originalPosition, state.editorCanvas.width, state.editorCanvas.height);

                    state.editorCtx.save();

                    // Scale font size proportionally
                    const fontSize = parseInt(settings.fontSize) || settings.fontSize;
                    const scaledFontSize = fontSize * (state.editorCanvas.width / 800);

                    // Build font string with weight and style
                    let fontString = '';
                    if (settings.fontWeight) fontString += settings.fontWeight + ' ';
                    if (settings.fontStyle) fontString += settings.fontStyle + ' ';
                    fontString += `${scaledFontSize}px ${settings.fontFamily}`;

                    state.editorCtx.font = fontString;
                    state.editorCtx.textAlign = scaledPosition.align;
                    state.editorCtx.fillStyle = settings.color;

                    // Add letter spacing if specified
                    if (settings.letterSpacing) {
                        state.editorCtx.letterSpacing = settings.letterSpacing;
                    }

                    // Add shadow if enabled
                    if (settings.shadow) {
                        const shadowWidth = settings.shadowWidth !== undefined ? settings.shadowWidth : 4;
                        const scaledShadowWidth = shadowWidth * (state.editorCanvas.width / 800);
                        state.editorCtx.shadowColor = 'rgba(0,0,0,0.7)';
                        state.editorCtx.shadowBlur = scaledShadowWidth;
                        state.editorCtx.shadowOffsetX = scaledShadowWidth / 2;
                        state.editorCtx.shadowOffsetY = scaledShadowWidth / 2;
                    }

                    // Draw text with scaled position
                    state.editorCtx.fillText(text, scaledPosition.x, scaledPosition.y);

                    state.editorCtx.restore();
                }
            });
        }

        // Global version of getScaledPosition
        function getScaledPositionGlobal(originalPos, canvasWidth, canvasHeight) {
            const scaleX = canvasWidth / 800;
            const scaleY = canvasHeight / 400;

            return {
                x: originalPos.x * scaleX,
                y: originalPos.y * scaleY,
                width: originalPos.width ? originalPos.width * scaleX : undefined,
                height: originalPos.height ? originalPos.height * scaleY : undefined,
                align: originalPos.align
            };
        }

        // Universal Editor Canvas Update - Works for any purpose
        function updateUniversalEditorCanvas() {
            const state = window.editorState;
            if (!state || !state.currentTemplate || !state.currentPurpose) {
                console.log('updateUniversalEditorCanvas: Missing required state');
                return;
            }

            // Get canvas elements
            const editorCanvas = document.getElementById('editorCanvas');
            const editorCtx = editorCanvas ? editorCanvas.getContext('2d') : null;

            if (!editorCanvas || !editorCtx) {
                console.log('updateUniversalEditorCanvas: Canvas not found');
                return;
            }

            // Set canvas size to match container
            const container = editorCanvas.parentElement;
            const containerRect = container.getBoundingClientRect();
            editorCanvas.width = containerRect.width;
            editorCanvas.height = containerRect.height;

            // Clear canvas
            editorCtx.clearRect(0, 0, editorCanvas.width, editorCanvas.height);

            // Get template configuration
            const template = state.templateConfig;
            const purposeConfig = state.purposeConfig;

            if (!template || !purposeConfig) {
                console.log('updateUniversalEditorCanvas: Template configuration not found');
                return;
            }

            // Draw background
            if (template.backgroundImage) {
                // Draw background image (for Anniversary templates)
                const img = new Image();
                img.crossOrigin = 'anonymous';
                img.onload = function() {
                    editorCtx.drawImage(img, 0, 0, editorCanvas.width, editorCanvas.height);
                    // Draw text after background loads
                    setTimeout(() => {
                        drawUniversalEditorText(editorCanvas, editorCtx, template, purposeConfig);
                    }, 20);
                };
                img.onerror = function() {
                    console.warn('Failed to load background image, using fallback');
                    // Fallback to solid color
                    editorCtx.fillStyle = '#000000';
                    editorCtx.fillRect(0, 0, editorCanvas.width, editorCanvas.height);
                    drawUniversalEditorText(editorCanvas, editorCtx, template, purposeConfig);
                };
                img.src = template.backgroundImage;
            } else if (template.backgroundColor) {
                // Draw solid color background
                editorCtx.fillStyle = template.backgroundColor;
                editorCtx.fillRect(0, 0, editorCanvas.width, editorCanvas.height);
                // Draw text immediately
                drawUniversalEditorText(editorCanvas, editorCtx, template, purposeConfig);
            } else {
                // No background, just draw text
                drawUniversalEditorText(editorCanvas, editorCtx, template, purposeConfig);
            }
        }

        // Draw text for universal templates
        function drawUniversalEditorText(canvas, ctx, template, purposeConfig) {
            if (!template.textPositions || !template.textStyles) return;

            // Get current text values from inputs
            const textInputs = ['editorLine1', 'editorLine2', 'editorLine3', 'editorLine4'];

            textInputs.forEach((inputId, index) => {
                const input = document.getElementById(inputId);
                if (!input || !input.value.trim()) return;

                const text = input.value.trim();
                const position = template.textPositions[index];
                const style = template.textStyles[index];

                if (!position || !style) return;

                // Scale position for canvas size
                const scaleX = canvas.width / 800;
                const scaleY = canvas.height / 400;
                const x = position.x * scaleX;
                const y = position.y * scaleY;

                // Apply text style
                ctx.save();
                ctx.fillStyle = style.color || '#ffffff';
                ctx.textAlign = position.align || 'center';
                ctx.textBaseline = 'middle';

                // Build font string
                let fontString = '';
                if (style.fontStyle) fontString += style.fontStyle + ' ';
                if (style.fontWeight) fontString += style.fontWeight + ' ';

                const fontSize = parseInt(style.fontSize) || 24;
                const scaledFontSize = fontSize * Math.min(scaleX, scaleY);
                fontString += scaledFontSize + 'px ';
                fontString += style.fontFamily || 'Arial';

                ctx.font = fontString;

                // Apply letter spacing if specified
                if (style.letterSpacing && style.letterSpacing !== 'normal') {
                    // For letter spacing, we need to draw each character separately
                    const spacing = parseFloat(style.letterSpacing) || 0;
                    let currentX = x - (ctx.measureText(text).width / 2);

                    for (let i = 0; i < text.length; i++) {
                        ctx.fillText(text[i], currentX, y);
                        currentX += ctx.measureText(text[i]).width + spacing;
                    }
                } else {
                    // Normal text drawing
                    ctx.fillText(text, x, y);
                }

                ctx.restore();
            });
        }

        // Draw Editor Background
        function drawEditorBackground(template) {
            // Check if template has a color background (like benefit-template-2)
            if (template && template.background && template.background.startsWith('#')) {
                // Draw template-defined color background
                editorCtx.fillStyle = template.background;
                editorCtx.fillRect(0, 0, editorCanvas.width, editorCanvas.height);
                console.log('Template color background drawn:', template.background);

                // Draw text and image after background
                setTimeout(() => {
                    drawEditorText(template);
                    drawEditorImage(template);
                    console.log('Text and image drawing completed');
                }, 20);
            } else if (selectedBackgroundType === 'color') {
                // Draw user-selected solid color background
                editorCtx.fillStyle = selectedBackgroundColor;
                editorCtx.fillRect(0, 0, editorCanvas.width, editorCanvas.height);
                console.log('Color background drawn:', selectedBackgroundColor);

                // Draw text and image after background
                setTimeout(() => {
                    drawEditorText(template);
                    drawEditorImage(template);
                    console.log('Text and image drawing completed');
                }, 20);
            } else {
                // Draw image background - determine which background to use based on purpose
                let backgroundName = null;
                if (currentPurpose === 'Anniversary') {
                    backgroundName = selectedAnniversaryBackground;
                } else if (currentPurpose === 'Benefit') {
                    backgroundName = selectedBenefitBackground;
                }

                if (!backgroundName) {
                    console.error('No background name found for purpose:', currentPurpose);
                    return;
                }

                const backgroundUrl = `https://www.borgesmedia.com/wp-content/uploads/2025/06/${backgroundName}.png`;
                const img = new Image();
                img.crossOrigin = 'anonymous';
                img.onload = function() {
                    editorCtx.drawImage(img, 0, 0, editorCanvas.width, editorCanvas.height);
                    console.log('Background image loaded and drawn');

                    // Draw text and image after background loads
                    setTimeout(() => {
                        drawEditorText(template);
                        drawEditorImage(template);
                        console.log('Text and image drawing completed');
                    }, 20);
                };
                img.onerror = function() {
                    console.error('Failed to load background image:', backgroundUrl);
                    // Still try to draw text and image even if background fails
                    setTimeout(() => {
                        drawEditorText(template);
                        drawEditorImage(template);
                    }, 20);
                };
                img.src = backgroundUrl;
            }
        }

        // Draw Editor Text
        function drawEditorText(template) {
            const textInputs = ['editorLine1', 'editorLine2', 'editorLine3', 'editorLine4'];

            textInputs.forEach((lineId, index) => {
                const input = document.getElementById(lineId);
                const text = input ? input.value : '';

                if (text.trim()) {
                    // Use editor settings (which may have been initialized from template styles)
                    let settings;
                    if (window.editorState && window.editorState.editorTextSettings && window.editorState.editorTextSettings[lineId]) {
                        settings = window.editorState.editorTextSettings[lineId];
                    } else {
                        settings = editorTextSettings[lineId];
                    }

                    const originalPosition = template.textPositions[index];
                    const scaledPosition = getScaledPosition(originalPosition, editorCanvas.width, editorCanvas.height);

                    editorCtx.save();

                    // Scale font size proportionally
                    const fontSize = parseInt(settings.fontSize) || settings.fontSize;
                    const scaledFontSize = fontSize * (editorCanvas.width / 800);

                    // Build font string with weight and style
                    let fontString = '';
                    if (settings.fontWeight) fontString += settings.fontWeight + ' ';
                    if (settings.fontStyle) fontString += settings.fontStyle + ' ';
                    fontString += `${scaledFontSize}px ${settings.fontFamily}`;

                    editorCtx.font = fontString;
                    editorCtx.textAlign = scaledPosition.align;
                    editorCtx.fillStyle = settings.color;

                    // Add letter spacing if specified
                    if (settings.letterSpacing) {
                        editorCtx.letterSpacing = settings.letterSpacing;
                    }

                    // Add shadow if enabled
                    if (settings.shadow) {
                        const shadowWidth = settings.shadowWidth !== undefined ? settings.shadowWidth : 4;
                        const scaledShadowWidth = shadowWidth * (editorCanvas.width / 800);
                        editorCtx.shadowColor = 'rgba(0,0,0,0.7)';
                        editorCtx.shadowBlur = scaledShadowWidth;
                        editorCtx.shadowOffsetX = scaledShadowWidth / 2;
                        editorCtx.shadowOffsetY = scaledShadowWidth / 2;
                    }

                    // Draw text with scaled position
                    editorCtx.fillText(text, scaledPosition.x, scaledPosition.y);

                    editorCtx.restore();
                }
            });
        }

        // Draw Editor Image
        function drawEditorImage(template) {
            // Skip image drawing for text-only templates
            if (!template.imagePosition || template.type === 'text-only') return;

            // Check both local and global state for uploaded image
            const globalUploadedImage = window.editorState ? window.editorState.editorUploadedImage : null;
            const imageFromState = editorUploadedImage || globalUploadedImage;

            console.log('Drawing editor image, local image exists:', !!editorUploadedImage, 'global image exists:', !!globalUploadedImage);

            const originalPos = template.imagePosition;
            const scaledPos = getScaledPosition(originalPos, editorCanvas.width, editorCanvas.height);

            if (template.type === 'full-image') {
                // Template 1: Full-size image
                const imageToUse = imageFromState || template.defaultImage;
                if (imageToUse) {
                    if (typeof imageToUse === 'string') {
                        // Load default image
                        const img = new Image();
                        img.crossOrigin = 'anonymous';
                        img.onload = function() {
                            editorCtx.drawImage(img, scaledPos.x, scaledPos.y, scaledPos.width, scaledPos.height);
                            console.log('Default image drawn for full-image template');
                        };
                        img.src = imageToUse;
                    } else {
                        // Draw uploaded image
                        try {
                            editorCtx.drawImage(imageToUse, scaledPos.x, scaledPos.y, scaledPos.width, scaledPos.height);
                            console.log('Uploaded image drawn for full-image template');
                        } catch (error) {
                            console.error('Error drawing uploaded image:', error);
                        }
                    }
                }
            } else if (template.type === 'placeholder-space') {
                // Template 2: Placeholder space
                if (imageFromState) {
                    try {
                        editorCtx.drawImage(imageFromState, scaledPos.x, scaledPos.y, scaledPos.width, scaledPos.height);
                        console.log('Uploaded image drawn for placeholder-space template');
                    } catch (error) {
                        console.error('Error drawing uploaded image for placeholder:', error);
                    }
                } else {
                    // Draw placeholder with scaled dimensions
                    editorCtx.save();
                    editorCtx.strokeStyle = '#ddd';
                    const scaledDash = 10 * (editorCanvas.width / 800);
                    editorCtx.setLineDash([scaledDash, scaledDash / 2]);
                    editorCtx.lineWidth = 2 * (editorCanvas.width / 800);
                    editorCtx.strokeRect(scaledPos.x, scaledPos.y, scaledPos.width, scaledPos.height);

                    // Placeholder text with scaled font
                    editorCtx.fillStyle = '#666';
                    const scaledFontSize = 16 * (editorCanvas.width / 800);
                    editorCtx.font = `${scaledFontSize}px Arial`;
                    editorCtx.textAlign = 'center';
                    //editorCtx.fillText(template.placeholderText, scaledPos.x + scaledPos.width/2, scaledPos.y + scaledPos.height/2);
                    editorCtx.restore();
                    console.log('Placeholder drawn for placeholder-space template');
                }
            } else if (template.type === 'centered-image') {
                // Template 3: Centered smaller image
                const imageToUse = imageFromState || template.defaultImage;
                if (imageToUse) {
                    if (typeof imageToUse === 'string') {
                        // Load default image
                        const img = new Image();
                        img.crossOrigin = 'anonymous';
                        img.onload = function() {
                            // Draw circular image with scaled dimensions
                            editorCtx.save();
                            editorCtx.beginPath();
                            editorCtx.arc(scaledPos.x + scaledPos.width/2, scaledPos.y + scaledPos.height/2, scaledPos.width/2, 0, 2 * Math.PI);
                            editorCtx.clip();
                            editorCtx.drawImage(img, scaledPos.x, scaledPos.y, scaledPos.width, scaledPos.height);
                            editorCtx.restore();

                            // Draw white border with scaled width
                            editorCtx.beginPath();
                            editorCtx.arc(scaledPos.x + scaledPos.width/2, scaledPos.y + scaledPos.height/2, scaledPos.width/2, 0, 2 * Math.PI);
                            editorCtx.strokeStyle = 'white';
                            editorCtx.lineWidth = 6 * (editorCanvas.width / 800);
                            editorCtx.stroke();
                            console.log('Default image drawn for centered-image template');
                        };
                        img.src = imageToUse;
                    } else {
                        // Draw uploaded image (circular) with scaled dimensions
                        try {
                            editorCtx.save();
                            editorCtx.beginPath();
                            editorCtx.arc(scaledPos.x + scaledPos.width/2, scaledPos.y + scaledPos.height/2, scaledPos.width/2, 0, 2 * Math.PI);
                            editorCtx.clip();
                            editorCtx.drawImage(imageFromState, scaledPos.x, scaledPos.y, scaledPos.width, scaledPos.height);
                            editorCtx.restore();

                            // Draw white border with scaled width
                            editorCtx.beginPath();
                            editorCtx.arc(scaledPos.x + scaledPos.width/2, scaledPos.y + scaledPos.height/2, scaledPos.width/2, 0, 2 * Math.PI);
                            editorCtx.strokeStyle = 'white';
                            editorCtx.lineWidth = 6 * (editorCanvas.width / 800);
                            editorCtx.stroke();
                            console.log('Uploaded image drawn for centered-image template');
                        } catch (error) {
                            console.error('Error drawing uploaded image for centered template:', error);
                        }
                    }
                }
            }
        }

        // Handle Editor Photo Upload
        function handleEditorPhotoUpload(event) {
            const file = event.target.files[0];
            const validationDiv = document.getElementById('editorPhotoValidation');

            if (!file) {
                if (validationDiv) {
                    validationDiv.style.display = 'none';
                }
                return;
            }

            // Validate file format
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
            if (!allowedTypes.includes(file.type.toLowerCase())) {
                showPhotoValidation('error', 'Please upload a JPG or PNG image file only.');
                event.target.value = ''; // Clear the input
                return;
            }

            // Show success message for correct format
            showPhotoValidation('success', 'File format accepted. Processing image...');

            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = new Image();
                    img.onload = function() {
                        // Check image resolution
                        const width = img.naturalWidth;
                        const height = img.naturalHeight;
                        const minRecommendedWidth = 1200;
                        const minRecommendedHeight = 800;

                        if (width < minRecommendedWidth || height < minRecommendedHeight) {
                            showPhotoValidation('warning',
                                `Image resolution is ${width}x${height}px. For best quality, we recommend at least ${minRecommendedWidth}x${minRecommendedHeight}px.`);
                        } else {
                            showPhotoValidation('success',
                                `Great! High resolution image (${width}x${height}px) uploaded successfully.`);
                        }

                        // Show image resize dialog instead of directly setting the image
                        showImageResizeDialog(img);
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        }

        // Show photo validation message
        function showPhotoValidation(type, message) {
            const validationDiv = document.getElementById('editorPhotoValidation');
            if (validationDiv) {
                validationDiv.className = `validation-message ${type}`;
                validationDiv.textContent = message;
                validationDiv.style.display = 'block';
            }
        }

        // Validate text input and update character counter
        function validateTextInput(input) {
            const maxLength = 50;
            const currentLength = input.value.length;
            const lineNum = input.id.replace('editorLine', '');
            const counter = document.getElementById(`editorCounter${lineNum}`);

            if (counter) {
                counter.textContent = `${currentLength}/${maxLength} characters`;

                // Update counter styling based on character count
                counter.classList.remove('warning', 'error');
                if (currentLength >= maxLength * 0.9) { // 90% of limit
                    counter.classList.add('error');
                } else if (currentLength >= maxLength * 0.8) { // 80% of limit
                    counter.classList.add('warning');
                }
            }
        }

        // Show Background Graphic Dialog with state restoration
        function showBackgroundGraphicDialog() {
            const dialog = document.getElementById('backgroundGraphicDialog');
            if (dialog) {
                dialog.classList.add('show');

                // Restore background selection state when dialog opens
                setTimeout(() => {
                    restoreBackgroundDialogState();
                }, 100);
            }
        }

        // Restore background dialog state from saved data
        function restoreBackgroundDialogState() {
            console.log('restoreBackgroundDialogState: Restoring background dialog state...');

            try {
                // Get saved background state
                const savedBackgroundState = localStorage.getItem('editorBackgroundState');
                if (savedBackgroundState) {
                    const backgroundState = JSON.parse(savedBackgroundState);
                    console.log('restoreBackgroundDialogState: Found saved background state:', backgroundState);

                    // Restore background type selection
                    if (backgroundState.type) {
                        const typeButtons = document.querySelectorAll('.background-type-btn');
                        typeButtons.forEach(btn => {
                            btn.classList.remove('active');
                            if (btn.dataset.type === backgroundState.type) {
                                btn.classList.add('active');
                            }
                        });

                        // Show/hide appropriate sections
                        const imageSection = document.getElementById('imageBackgroundSection');
                        const colorSection = document.getElementById('colorBackgroundSection');
                        if (imageSection && colorSection) {
                            if (backgroundState.type === 'image') {
                                imageSection.style.display = 'block';
                                colorSection.style.display = 'none';
                            } else {
                                imageSection.style.display = 'none';
                                colorSection.style.display = 'block';
                            }
                        }
                    }

                    // Restore graphic selection
                    if (backgroundState.graphic) {
                        const backgroundOptions = document.querySelectorAll('.anniversary-bg-option');
                        backgroundOptions.forEach(option => {
                            option.classList.remove('selected');
                            if (option.dataset.background === backgroundState.graphic) {
                                option.classList.add('selected');
                            }
                        });
                    }

                    // Update global state
                    if (window.editorState) {
                        window.editorState.selectedBackgroundType = backgroundState.type || 'image';
                        window.editorState.selectedAnniversaryBackground = backgroundState.graphic || 'Anniversary-1';
                        window.editorState.selectedBackgroundColor = backgroundState.color || '#ff0000';
                    }
                }

                console.log('restoreBackgroundDialogState: Background dialog state restored');
            } catch (error) {
                console.error('restoreBackgroundDialogState: Error restoring background state:', error);
            }
        }

        // Update Background Selection in Dialog
        function updateBackgroundSelection() {
            const grid = document.getElementById('anniversaryBackgroundsGrid');
            if (!grid) return;

            // Remove previous selection
            grid.querySelectorAll('.anniversary-bg-option').forEach(opt => {
                opt.classList.remove('selected');
            });

            // Select current background based on purpose
            let currentBackground = null;
            if (currentPurpose === 'Anniversary') {
                currentBackground = selectedAnniversaryBackground;
            } else if (currentPurpose === 'Benefit') {
                currentBackground = selectedBenefitBackground;
            }

            if (currentBackground) {
                const currentOption = grid.querySelector(`[data-background="${currentBackground}"]`);
                if (currentOption) {
                    currentOption.classList.add('selected');
                }
            }
        }

        // Initialize Anniversary Backgrounds
        function initAnniversaryBackgrounds() {
            const grid = document.getElementById('anniversaryBackgroundsGrid');
            if (!grid) return;

            // Create anniversary background options (1-15)
            for (let i = 1; i <= 15; i++) {
                const option = document.createElement('div');
                option.className = 'anniversary-bg-option';
                option.dataset.background = `Anniversary-${i}`;

                const img = document.createElement('img');
                img.src = `https://www.borgesmedia.com/wp-content/uploads/2025/06/Anniversary-${i}.png`;
                img.alt = `Anniversary ${i}`;

                option.appendChild(img);
                grid.appendChild(option);

                // Add click handler
                option.addEventListener('click', function() {
                    // Remove previous selection
                    grid.querySelectorAll('.anniversary-bg-option').forEach(opt => {
                        opt.classList.remove('selected');
                    });

                    // Select current
                    this.classList.add('selected');
                    selectedAnniversaryBackground = this.dataset.background;
                    syncEditorState();
                });
            }

            // Set initial selection
            updateBackgroundSelection();

            // Dialog buttons
            const selectBtn = document.getElementById('selectBackgroundBtn');
            const cancelBtn = document.getElementById('cancelBackgroundBtn');
            const dialog = document.getElementById('backgroundGraphicDialog');

            if (selectBtn) {
                selectBtn.addEventListener('click', function() {
                    dialog.classList.remove('show');
                    updateEditorCanvas();
                });
            }

            if (cancelBtn) {
                cancelBtn.addEventListener('click', function() {
                    dialog.classList.remove('show');
                });
            }

            // Close dialog on background click
            if (dialog) {
                dialog.addEventListener('click', function(e) {
                    if (e.target === dialog) {
                        dialog.classList.remove('show');
                    }
                });
            }

            // Initialize background type switching
            initBackgroundTypeSwitching();
        }

        // Initialize Benefit Backgrounds
        function initBenefitBackgrounds() {
            const grid = document.getElementById('anniversaryBackgroundsGrid'); // Reuse the same grid
            if (!grid) return;

            // Clear existing options
            grid.innerHTML = '';

            // Create benefit background options (3-9 based on the template data)
            const benefitBackgrounds = [3, 4, 5, 6, 8, 9]; // Available benefit backgrounds
            benefitBackgrounds.forEach(i => {
                const option = document.createElement('div');
                option.className = 'anniversary-bg-option'; // Reuse the same CSS class
                option.dataset.background = `Benefit-${i}`;

                const img = document.createElement('img');
                img.src = `https://www.borgesmedia.com/wp-content/uploads/2025/06/Benefit-${i}.png`;
                img.alt = `Benefit ${i}`;

                option.appendChild(img);
                grid.appendChild(option);

                // Add click handler
                option.addEventListener('click', function() {
                    // Remove previous selection
                    grid.querySelectorAll('.anniversary-bg-option').forEach(opt => {
                        opt.classList.remove('selected');
                    });

                    // Select current
                    this.classList.add('selected');
                    selectedBenefitBackground = this.dataset.background;
                    syncEditorState();
                });
            });

            // Set initial selection
            updateBackgroundSelection();

            // Dialog buttons (reuse existing ones)
            const selectBtn = document.getElementById('selectBackgroundBtn');
            const cancelBtn = document.getElementById('cancelBackgroundBtn');
            const dialog = document.getElementById('backgroundGraphicDialog');

            if (selectBtn) {
                selectBtn.addEventListener('click', function() {
                    dialog.classList.remove('show');
                    updateEditorCanvas();
                });
            }

            if (cancelBtn) {
                cancelBtn.addEventListener('click', function() {
                    dialog.classList.remove('show');
                });
            }

            // Close dialog on background click
            if (dialog) {
                dialog.addEventListener('click', function(e) {
                    if (e.target === dialog) {
                        dialog.classList.remove('show');
                    }
                });
            }

            // Initialize background type switching
            initBackgroundTypeSwitching();
        }

        // Initialize Background Type Switching
        function initBackgroundTypeSwitching() {
            const imageBtn = document.getElementById('imageBackgroundBtn');
            const colorBtn = document.getElementById('colorBackgroundBtn');
            const imageSection = document.getElementById('imageBackgroundSection');
            const colorSection = document.getElementById('colorBackgroundSection');

            if (!imageBtn || !colorBtn || !imageSection || !colorSection) {
                console.warn('Background type switching elements not found');
                return;
            }

            // Image background button click
            imageBtn.addEventListener('click', function() {
                selectedBackgroundType = 'image';
                syncEditorState();

                // Update button states
                imageBtn.classList.add('active');
                colorBtn.classList.remove('active');

                // Show/hide sections
                imageSection.style.display = 'block';
                colorSection.style.display = 'none';
            });

            // Color background button click
            colorBtn.addEventListener('click', function() {
                selectedBackgroundType = 'color';
                syncEditorState();

                // Update button states
                colorBtn.classList.add('active');
                imageBtn.classList.remove('active');

                // Show/hide sections
                imageSection.style.display = 'none';
                colorSection.style.display = 'block';
            });

            // Initialize background color picker
            initBackgroundColorPicker();
        }

        // Initialize Background Color Picker
        function initBackgroundColorPicker() {
            const gradientArea = document.getElementById('backgroundColorGradient');
            const hueSlider = document.getElementById('backgroundHueSlider');
            const colorPreview = document.getElementById('backgroundColorPreview');
            const resetBtn = document.getElementById('backgroundColorReset');

            if (!gradientArea || !hueSlider || !colorPreview || !resetBtn) {
                console.warn('Background color picker elements not found');
                return;
            }

            let currentHue = 0;
            let currentSaturation = 100;
            let currentLightness = 50;

            // Convert HSL to RGB
            function hslToRgb(h, s, l) {
                h /= 360;
                s /= 100;
                l /= 100;

                const a = s * Math.min(l, 1 - l);
                const f = n => {
                    const k = (n + h * 12) % 12;
                    return l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
                };

                return [Math.round(f(0) * 255), Math.round(f(8) * 255), Math.round(f(4) * 255)];
            }

            // Convert RGB to Hex
            function rgbToHex(r, g, b) {
                return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
            }

            // Update color preview and selected color
            function updateBackgroundColor() {
                const [r, g, b] = hslToRgb(currentHue, currentSaturation, currentLightness);
                const hexColor = rgbToHex(r, g, b);
                colorPreview.style.backgroundColor = hexColor;
                selectedBackgroundColor = hexColor;
                syncEditorState();
            }

            // Update gradient background based on hue
            function updateGradientBackground() {
                const [r, g, b] = hslToRgb(currentHue, 100, 50);
                const hueColor = rgbToHex(r, g, b);
                gradientArea.style.background = `linear-gradient(to right, #fff, ${hueColor})`;
            }

            // Hue slider interaction
            hueSlider.addEventListener('mousedown', function(e) {
                const rect = hueSlider.getBoundingClientRect();
                const y = e.clientY - rect.top;
                const percentage = Math.max(0, Math.min(100, (y / rect.height) * 100));

                currentHue = (percentage / 100) * 360;
                const handle = hueSlider.querySelector('#backgroundHueHandle');
                if (handle) {
                    handle.style.top = percentage + '%';
                }

                updateGradientBackground();
                updateBackgroundColor();
            });

            // Gradient area interaction
            gradientArea.addEventListener('mousedown', function(e) {
                const rect = gradientArea.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                const saturation = Math.max(0, Math.min(100, (x / rect.width) * 100));
                const lightness = Math.max(0, Math.min(100, 100 - (y / rect.height) * 100));

                currentSaturation = saturation;
                currentLightness = lightness;

                const picker = gradientArea.querySelector('#backgroundColorPicker');
                if (picker) {
                    picker.style.left = (x / rect.width) * 100 + '%';
                    picker.style.top = (y / rect.height) * 100 + '%';
                }

                updateBackgroundColor();
            });

            // Reset button
            resetBtn.addEventListener('click', function() {
                currentHue = 0;
                currentSaturation = 100;
                currentLightness = 50;

                const handle = hueSlider.querySelector('#backgroundHueHandle');
                if (handle) {
                    handle.style.top = '0%';
                }

                const picker = gradientArea.querySelector('#backgroundColorPicker');
                if (picker) {
                    picker.style.left = '100%';
                    picker.style.top = '50%';
                }

                updateGradientBackground();
                updateBackgroundColor();
            });

            // Initialize
            updateGradientBackground();
            updateBackgroundColor();
        }

        // Confirm Template Design - Make it globally accessible
        function confirmTemplateDesign() {
            // Copy editor data to main form
            const editorData = {
                template: currentAnniversaryTemplate,
                background: selectedAnniversaryBackground,
                backgroundType: selectedBackgroundType,
                backgroundColor: selectedBackgroundColor,
                textLines: {
                    line1: document.getElementById('editorLine1')?.value || '',
                    line2: document.getElementById('editorLine2')?.value || '',
                    line3: document.getElementById('editorLine3')?.value || '',
                    line4: document.getElementById('editorLine4')?.value || ''
                },
                textSettings: editorTextSettings,
                hasUploadedImage: !!editorUploadedImage
            };

            // Update main form fields
            document.getElementById('line1').value = editorData.textLines.line1;
            document.getElementById('line2').value = editorData.textLines.line2;
            document.getElementById('line3').value = editorData.textLines.line3;
            document.getElementById('line4').value = editorData.textLines.line4;

            // Update hidden fields
            document.getElementById('selectedTemplate').value = currentAnniversaryTemplate;
            document.getElementById('billboardData').value = JSON.stringify(editorData);

            // Generate canvas image for preview
            const editorCanvas = document.getElementById('editorCanvas');
            if (editorCanvas) {
                const canvasDataURL = editorCanvas.toDataURL('image/png');
                localStorage.setItem('adPreviewImage', canvasDataURL);
            }

            // Mark design as confirmed but keep editor open
            localStorage.setItem('designConfirmed', 'true');
            localStorage.setItem('editorShouldStayOpen', 'true');

            // Update editor title to show confirmed status
            if (window.updateEditorTitle) {
                window.updateEditorTitle('confirmed');
            }

            // Show success message without closing editor
            if (window.showDesignConfirmationMessage) {
                window.showDesignConfirmationMessage();
            } else {
                alert('Template design confirmed! Continue editing or proceed to checkout.');
            }

            console.log('confirmTemplateDesign: Design confirmed, editor remains open');
        }

        // Make confirmTemplateDesign globally accessible
        window.confirmTemplateDesign = confirmTemplateDesign;

        // Create a robust global version that doesn't depend on local scope
        window.confirmTemplateDesignGlobal = function() {
            // Prevent multiple simultaneous executions
            if (window.confirmTemplateDesignGlobal.isRunning) {
                console.log('confirmTemplateDesignGlobal: Already running, skipping...');
                return;
            }

            window.confirmTemplateDesignGlobal.isRunning = true;

            try {
                console.log('confirmTemplateDesignGlobal: Starting...');

                // Get editor data from DOM elements directly with null checks
                const editorLine1 = document.getElementById('editorLine1');
                const editorLine2 = document.getElementById('editorLine2');
                const editorLine3 = document.getElementById('editorLine3');
                const editorLine4 = document.getElementById('editorLine4');

                if (!editorLine1 || !editorLine2 || !editorLine3 || !editorLine4) {
                    console.error('confirmTemplateDesignGlobal: Editor input elements not found');
                    alert('Error: Editor elements not found. Please try again.');
                    return;
                }

                // Get current template from global state or DOM
                const currentTemplate = window.editorState?.currentAnniversaryTemplate ||
                                    localStorage.getItem('currentAnniversaryTemplate') ||
                                    'anniversary-template-1';

                const selectedBackground = window.editorState?.selectedAnniversaryBackground ||
                                        localStorage.getItem('selectedAnniversaryBackground') ||
                                        'Anniversary-1';

                const backgroundType = window.editorState?.selectedBackgroundType ||
                                    localStorage.getItem('selectedBackgroundType') ||
                                    'image';

                const backgroundColor = window.editorState?.selectedBackgroundColor ||
                                    localStorage.getItem('selectedBackgroundColor') ||
                                    '#ff0000';

                // Create editor data object
                const editorData = {
                    template: currentTemplate,
                    background: selectedBackground,
                    backgroundType: backgroundType,
                    backgroundColor: backgroundColor,
                    textLines: {
                        line1: editorLine1.value || '',
                        line2: editorLine2.value || '',
                        line3: editorLine3.value || '',
                        line4: editorLine4.value || ''
                    },
                    textSettings: window.editorState?.editorTextSettings || {
                        editorLine1: { color: '#ffffff', fontSize: 50, fontFamily: 'Arial', shadow: true, shadowWidth: 4 },
                        editorLine2: { color: '#ffffff', fontSize: 50, fontFamily: 'Arial', shadow: true, shadowWidth: 4 },
                        editorLine3: { color: '#ffffff', fontSize: 50, fontFamily: 'Arial', shadow: true, shadowWidth: 4 },
                        editorLine4: { color: '#ffffff', fontSize: 50, fontFamily: 'Arial', shadow: true, shadowWidth: 4 }
                    },
                    hasUploadedImage: !!(window.editorState?.editorUploadedImage)
                };

                console.log('confirmTemplateDesignGlobal: Editor data created:', editorData);

                // Update main form fields with comprehensive null checks
                const mainFormFields = {
                    line1: document.getElementById('line1'),
                    line2: document.getElementById('line2'),
                    line3: document.getElementById('line3'),
                    line4: document.getElementById('line4')
                };

                // Safely update main form fields
                Object.keys(mainFormFields).forEach(fieldName => {
                    const field = mainFormFields[fieldName];
                    const value = editorData.textLines[fieldName];
                    if (field && value !== undefined) {
                        field.value = value;
                        console.log(`confirmTemplateDesignGlobal: Updated ${fieldName} = "${value}"`);
                    } else if (!field) {
                        console.warn(`confirmTemplateDesignGlobal: Main form field ${fieldName} not found`);
                    }
                });

                // Update hidden fields with null checks
                const hiddenFields = {
                    selectedTemplate: document.getElementById('selectedTemplate'),
                    billboardData: document.getElementById('billboardData')
                };

                if (hiddenFields.selectedTemplate) {
                    hiddenFields.selectedTemplate.value = currentTemplate;
                    console.log('confirmTemplateDesignGlobal: Updated selectedTemplate field');
                } else {
                    console.warn('confirmTemplateDesignGlobal: selectedTemplate field not found');
                }

                if (hiddenFields.billboardData) {
                    hiddenFields.billboardData.value = JSON.stringify(editorData);
                    console.log('confirmTemplateDesignGlobal: Updated billboardData field');
                } else {
                    console.warn('confirmTemplateDesignGlobal: billboardData field not found');
                }

                // Store in localStorage for checkout
                localStorage.setItem('adDesignData', JSON.stringify(editorData));
                localStorage.setItem('currentAnniversaryTemplate', currentTemplate);
                localStorage.setItem('selectedAnniversaryBackground', selectedBackground);
                localStorage.setItem('selectedBackgroundType', backgroundType);
                localStorage.setItem('selectedBackgroundColor', backgroundColor);

                // Professional canvas image capture for template confirmation
                let canvasDataURL = null;
                const canvasSources = ['editorCanvas', 'billboardCanvas'];

                for (const canvasId of canvasSources) {
                    const canvas = document.getElementById(canvasId);
                    if (canvas) {
                        try {
                            // Check if canvas has content
                            const ctx = canvas.getContext('2d');
                            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                            const hasContent = imageData.data.some(pixel => pixel !== 0);

                            if (hasContent) {
                                canvasDataURL = canvas.toDataURL('image/png', 0.9);
                                localStorage.setItem('adPreviewImage', canvasDataURL);
                                localStorage.setItem('billboardCanvasImage', canvasDataURL);
                                console.log(`confirmTemplateDesignGlobal: Canvas image saved from ${canvasId}`);
                                break;
                            }
                        } catch (error) {
                            console.warn(`confirmTemplateDesignGlobal: Error capturing ${canvasId}:`, error);
                        }
                    }
                }

                // Generate preview if no canvas image captured
                if (!canvasDataURL) {
                    console.log('confirmTemplateDesignGlobal: Generating preview from design data...');
                    canvasDataURL = generatePreviewFromDesignData();
                    if (canvasDataURL) {
                        localStorage.setItem('adPreviewImage', canvasDataURL);
                        localStorage.setItem('billboardCanvasImage', canvasDataURL);
                        console.log('confirmTemplateDesignGlobal: Preview generated and saved');
                    }
                }

                // Professional UX: Keep editor open for continued editing
                // Instead of closing the editor, just show a success message and keep it accessible
                console.log('confirmTemplateDesignGlobal: Design confirmed, keeping editor open for continued editing');

                // Add a visual confirmation that design is saved
                showDesignConfirmationMessage();

                // Update the editor title to show it's confirmed
                updateEditorTitle('confirmed');

                // Show professional success message (non-blocking)
                console.log('confirmTemplateDesignGlobal: Completed successfully');

            } catch (error) {
                console.error('confirmTemplateDesignGlobal: Error occurred:', error);
                console.error('confirmTemplateDesignGlobal: Error stack:', error.stack);
                alert('An error occurred while confirming the design. Please try again.');
            } finally {
                // Always reset the running flag
                setTimeout(() => {
                    window.confirmTemplateDesignGlobal.isRunning = false;
                }, 500);
            }
        };

        // Professional visual confirmation functions
        window.showDesignConfirmationMessage = function() {
            // Remove any existing confirmation messages
            const existingMessages = document.querySelectorAll('.design-confirmation-message');
            existingMessages.forEach(msg => msg.remove());

            // Create professional confirmation message
            const confirmationDiv = document.createElement('div');
            confirmationDiv.className = 'design-confirmation-message';
            confirmationDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 15px 25px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
                z-index: 10000;
                font-family: Arial, sans-serif;
                font-size: 14px;
                font-weight: 600;
                animation: slideInRight 0.5s ease-out;
                max-width: 300px;
            `;

            confirmationDiv.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="font-size: 18px;">✅</span>
                    <div>
                        <div style="font-weight: 700; margin-bottom: 2px;">Design Confirmed!</div>
                        <div style="font-size: 12px; opacity: 0.9;">Continue editing or proceed to checkout</div>
                    </div>
                </div>
            `;

            // Add animation keyframes if not already added
            if (!document.querySelector('#confirmation-animations')) {
                const style = document.createElement('style');
                style.id = 'confirmation-animations';
                style.textContent = `
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes fadeOut {
                        from { opacity: 1; }
                        to { opacity: 0; }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(confirmationDiv);

            // Auto-remove after 4 seconds with fade out
            setTimeout(() => {
                confirmationDiv.style.animation = 'fadeOut 0.5s ease-out';
                setTimeout(() => {
                    if (confirmationDiv.parentNode) {
                        confirmationDiv.remove();
                    }
                }, 500);
            }, 4000);

            console.log('showDesignConfirmationMessage: Confirmation message displayed');
        };

        // Update editor title to show confirmation status
        window.updateEditorTitle = function(status) {
            const editorContainer = document.getElementById('templateEditorContainer');
            if (!editorContainer) return;

            let titleElement = editorContainer.querySelector('.editor-title-status');

            if (!titleElement) {
                // Create title status element if it doesn't exist
                titleElement = document.createElement('div');
                titleElement.className = 'editor-title-status';
                titleElement.style.cssText = `
                    text-align: center;
                    padding: 10px;
                    margin-bottom: 15px;
                    border-radius: 6px;
                    font-weight: 600;
                    font-size: 14px;
                `;

                // Insert at the beginning of the editor container
                const firstChild = editorContainer.firstElementChild;
                if (firstChild) {
                    editorContainer.insertBefore(titleElement, firstChild);
                } else {
                    editorContainer.appendChild(titleElement);
                }
            }

            if (status === 'confirmed') {
                titleElement.style.background = 'linear-gradient(135deg, #d4edda, #c3e6cb)';
                titleElement.style.color = '#155724';
                titleElement.style.border = '1px solid #c3e6cb';
                titleElement.innerHTML = `
                    <span style="font-size: 16px; margin-right: 8px;">✅</span>
                    Design Confirmed - Continue editing or proceed to checkout
                `;
            } else {
                titleElement.style.background = 'linear-gradient(135deg, #e3f2fd, #bbdefb)';
                titleElement.style.color = '#1565c0';
                titleElement.style.border = '1px solid #bbdefb';
                titleElement.innerHTML = `
                    <span style="font-size: 16px; margin-right: 8px;">🎨</span>
                    Customize Your Anniversary Billboard
                `;
            }

            console.log(`updateEditorTitle: Title updated to ${status} status`);
        };

        // Universal Confirm Template Design - Works for any purpose
        window.confirmUniversalTemplateDesign = function() {
            const state = window.editorState;
            if (!state || !state.currentTemplate || !state.currentPurpose) {
                console.log('confirmUniversalTemplateDesign: Missing required state');
                alert('Please select a template first.');
                return;
            }

            try {
                console.log('confirmUniversalTemplateDesign: Starting for purpose:', state.currentPurpose);

                // Get editor data
                const editorData = {
                    purpose: state.currentPurpose,
                    template: state.currentTemplate,
                    templateConfig: state.templateConfig,
                    textLines: {
                        line1: document.getElementById('editorLine1')?.value || '',
                        line2: document.getElementById('editorLine2')?.value || '',
                        line3: document.getElementById('editorLine3')?.value || '',
                        line4: document.getElementById('editorLine4')?.value || ''
                    },
                    hasUploadedImage: !!state.editorUploadedImage
                };

                // Update main form fields
                const mainFormFields = {
                    line1: document.getElementById('line1'),
                    line2: document.getElementById('line2'),
                    line3: document.getElementById('line3'),
                    line4: document.getElementById('line4')
                };

                Object.keys(mainFormFields).forEach(fieldName => {
                    const field = mainFormFields[fieldName];
                    const value = editorData.textLines[fieldName];
                    if (field && value !== undefined) {
                        field.value = value;
                        console.log(`confirmUniversalTemplateDesign: Updated ${fieldName} = "${value}"`);
                    }
                });

                // Update hidden fields
                const selectedTemplateField = document.getElementById('selectedTemplate');
                const billboardDataField = document.getElementById('billboardData');

                if (selectedTemplateField) {
                    selectedTemplateField.value = state.currentTemplate;
                }

                if (billboardDataField) {
                    billboardDataField.value = JSON.stringify(editorData);
                }

                // Store in localStorage for checkout
                localStorage.setItem('adDesignData', JSON.stringify(editorData));
                localStorage.setItem('currentTemplate', state.currentTemplate);
                localStorage.setItem('currentPurpose', state.currentPurpose);

                // Capture canvas image
                const editorCanvas = document.getElementById('editorCanvas');
                if (editorCanvas) {
                    try {
                        const canvasDataURL = editorCanvas.toDataURL('image/png', 0.9);
                        localStorage.setItem('adPreviewImage', canvasDataURL);
                        localStorage.setItem('billboardCanvasImage', canvasDataURL);
                        console.log('confirmUniversalTemplateDesign: Canvas image saved');
                    } catch (error) {
                        console.warn('confirmUniversalTemplateDesign: Error capturing canvas:', error);
                    }
                }

                // Show confirmation message
                showDesignConfirmationMessage();

                // Update editor title
                updateEditorTitle('confirmed');

                console.log('confirmUniversalTemplateDesign: Completed successfully');

            } catch (error) {
                console.error('confirmUniversalTemplateDesign: Error occurred:', error);
                alert('An error occurred while confirming the design. Please try again.');
            }
        };

        // Universal Confirm Design Router - Routes to appropriate confirm function
        window.confirmDesignUniversal = function() {
            const currentPurpose = localStorage.getItem('selectedPurpose');
            const state = window.editorState;

            console.log('confirmDesignUniversal: Current purpose:', currentPurpose);
            console.log('confirmDesignUniversal: Editor state:', state);

            if (currentPurpose === 'Anniversary') {
                // Use anniversary-specific confirm function
                if (window.confirmTemplateDesignGlobal) {
                    window.confirmTemplateDesignGlobal();
                } else if (window.confirmTemplateDesign) {
                    window.confirmTemplateDesign();
                } else {
                    console.error('confirmDesignUniversal: No anniversary confirm function available');
                    alert('Error: Unable to confirm anniversary design.');
                }
            } else if (UNIVERSAL_TEMPLATE_CONFIG[currentPurpose]) {
                // Use universal template confirm function
                window.confirmUniversalTemplateDesign();
            } else {
                // Fallback to global confirm function
                if (window.confirmTemplateDesignGlobal) {
                    window.confirmTemplateDesignGlobal();
                } else {
                    console.error('confirmDesignUniversal: No confirm function available');
                    alert('Error: Unable to confirm design.');
                }
            }
        };

        // Create a global version of backToTemplateSelection with debouncing and reset functionality
        window.backToTemplateSelection = function(shouldReset = true) {
            // Prevent multiple simultaneous executions
            if (window.backToTemplateSelection.isRunning) {
                console.log('backToTemplateSelection: Already running, skipping...');
                return;
            }

            window.backToTemplateSelection.isRunning = true;

            try {
                console.log('backToTemplateSelection: Starting with reset =', shouldReset);

                // Reset all fields if requested
                if (shouldReset) {
                    console.log('backToTemplateSelection: Resetting all fields...');

                    // Reset text input fields
                    const textInputs = ['line1', 'line2', 'line3', 'line4', 'editorLine1', 'editorLine2', 'editorLine3', 'editorLine4'];
                    textInputs.forEach(inputId => {
                        const input = document.getElementById(inputId);
                        if (input) {
                            input.value = '';
                        }
                    });

                    // Reset color buttons to default white
                    const colorButtons = ['colorBtn1', 'colorBtn2', 'colorBtn3', 'colorBtn4', 'editorColorBtn1', 'editorColorBtn2', 'editorColorBtn3', 'editorColorBtn4'];
                    colorButtons.forEach(btnId => {
                        const btn = document.getElementById(btnId);
                        if (btn) {
                            btn.style.backgroundColor = '#ffffff';
                        }
                    });

                    // Clear template selections
                    document.querySelectorAll('.template-card, .anniversary-template-card').forEach(card => {
                        card.classList.remove('selected');
                    });

                    // Clear localStorage related to design
                    localStorage.removeItem('adDesignData');
                    localStorage.removeItem('editorShouldStayOpen');
                    localStorage.removeItem('returningFromCheckout');

                    console.log('backToTemplateSelection: Fields reset completed');
                }

                // Check if editor should stay open (e.g., when returning from checkout)
                const editorShouldStayOpen = !shouldReset && localStorage.getItem('editorShouldStayOpen');
                const hasConfirmedDesign = !shouldReset && localStorage.getItem('adDesignData');
                const currentPurpose = localStorage.getItem('selectedPurpose');

                console.log('backToTemplateSelection: Current purpose:', currentPurpose);
                console.log('backToTemplateSelection: Has confirmed design:', !!hasConfirmedDesign);
                console.log('backToTemplateSelection: Editor should stay open:', !!editorShouldStayOpen);

                const editorContainer = document.getElementById('templateEditorContainer');

                // Only close editor if it's not supposed to stay open
                if (!editorShouldStayOpen && editorContainer) {
                    editorContainer.classList.remove('active');
                    console.log('backToTemplateSelection: Editor container hidden');
                } else if (editorShouldStayOpen && editorContainer) {
                    // Keep editor open and ensure it's active
                    editorContainer.classList.add('active');
                    console.log('backToTemplateSelection: Editor container kept open (editorShouldStayOpen flag)');
                } else if (!editorContainer) {
                    console.warn('backToTemplateSelection: Editor container not found');
                }

                // Update global state safely (but keep editor mode if editor should stay open)
                if (window.editorState) {
                    window.editorState.isEditorMode = !!editorShouldStayOpen;
                    console.log('backToTemplateSelection: Global state updated, isEditorMode:', window.editorState.isEditorMode);
                }

                if ((hasConfirmedDesign || editorShouldStayOpen) && (currentPurpose === 'Anniversary' || UNIVERSAL_TEMPLATE_CONFIG[currentPurpose])) {
                    // If we have a confirmed design or editor should stay open, keep the editor open
                    console.log('backToTemplateSelection: Keeping editor open for confirmed design or return from checkout');

                    if (currentPurpose === 'Anniversary') {
                        // Show anniversary templates section
                        const anniversarySection = document.getElementById('anniversaryTemplatesSection');
                        if (anniversarySection) {
                            anniversarySection.style.display = 'block';
                        }
                    } else if (currentPurpose === 'Benefit') {
                        // Show benefit templates section
                        const benefitSection = document.getElementById('benefitTemplatesSection');
                        if (benefitSection) {
                            benefitSection.style.display = 'block';
                        }
                    } else if (UNIVERSAL_TEMPLATE_CONFIG[currentPurpose]) {
                        // Show universal templates section
                        const universalSection = document.getElementById('universalTemplatesSection');
                        if (universalSection) {
                            universalSection.style.display = 'block';
                        }
                    }

                    // Ensure editor is open
                    if (editorContainer) {
                        editorContainer.classList.add('active');
                    }

                    // Keep editor open and restore its state
                    setTimeout(() => {
                        if (window.restoreBillboardDesign) {
                            window.restoreBillboardDesign();
                        }
                        updateEditorTitle('confirmed');
                    }, 500);

                } else if (currentPurpose && currentPurpose !== 'Select a Purpose') {
                    if (window.showTemplatesForPurpose) {
                        window.showTemplatesForPurpose(currentPurpose);
                        console.log('backToTemplateSelection: Called showTemplatesForPurpose');
                    } else {
                        // Fallback: show appropriate templates section
                        if (currentPurpose === 'Anniversary') {
                            const anniversarySection = document.getElementById('anniversaryTemplatesSection');
                            if (anniversarySection) {
                                anniversarySection.style.display = 'block';
                                console.log('backToTemplateSelection: Anniversary section shown (fallback)');
                            }
                        } else if (currentPurpose === 'Benefit') {
                            const benefitSection = document.getElementById('benefitTemplatesSection');
                            if (benefitSection) {
                                benefitSection.style.display = 'block';
                                console.log('backToTemplateSelection: Benefit section shown (fallback)');
                            }
                        } else if (UNIVERSAL_TEMPLATE_CONFIG[currentPurpose]) {
                            const universalSection = document.getElementById('universalTemplatesSection');
                            if (universalSection) {
                                universalSection.style.display = 'block';
                                console.log('backToTemplateSelection: Universal section shown (fallback)');
                            }
                        }
                    }
                } else {
                    if (window.showDefaultTemplateState) {
                        window.showDefaultTemplateState();
                        console.log('backToTemplateSelection: Called showDefaultTemplateState');
                    } else {
                        // Fallback: show template selection section
                        const templateSection = document.getElementById('templateSelectionSection');
                        if (templateSection) {
                            templateSection.style.display = 'block';
                            console.log('backToTemplateSelection: Template section shown (fallback)');
                        }
                    }
                }

                console.log('backToTemplateSelection: Completed successfully');
            } catch (error) {
                console.error('backToTemplateSelection: Error occurred:', error);
                console.error('backToTemplateSelection: Error stack:', error.stack);
            } finally {
                // Reset the running flag after a delay
                setTimeout(() => {
                    window.backToTemplateSelection.isRunning = false;
                }, 300);
            }
        };

        // Setup Editor Color Pickers
        function setupEditorColorPickers() {
            console.log('setupEditorColorPickers: Starting setup...');

            // Reset setup flag to allow re-setup if needed
            window.editorColorPickersSetup = false;

            ['1', '2', '3', '4'].forEach(lineNum => {
                const colorBtn = document.getElementById(`editorColorBtn${lineNum}`);
                const colorDropdown = document.getElementById(`editorColorDropdown${lineNum}`);
                const colorPaletteEl = document.getElementById(`editorColorPalette${lineNum}`);
                const lineId = `editorLine${lineNum}`;

                console.log(`setupEditorColorPickers: Line ${lineNum} - colorBtn:`, !!colorBtn, 'colorDropdown:', !!colorDropdown, 'colorPalette:', !!colorPaletteEl);

                // Check if all elements exist
                if (!colorBtn || !colorDropdown || !colorPaletteEl) {
                    console.warn(`Missing editor color picker elements for line ${lineNum}:`, {
                        colorBtn: !!colorBtn,
                        colorDropdown: !!colorDropdown,
                        colorPaletteEl: !!colorPaletteEl
                    });
                    return;
                }

                // Set initial color from global state
                const initialColor = (window.editorState && window.editorState.editorTextSettings && window.editorState.editorTextSettings[lineId])
                    ? window.editorState.editorTextSettings[lineId].color
                    : editorTextSettings[lineId].color;
                colorBtn.style.backgroundColor = initialColor;

                // Clear existing color palette content
                colorPaletteEl.innerHTML = '';

                // Create unified color picker
                try {
                    createUnifiedColorPicker(colorPaletteEl, colorDropdown, lineId, setEditorTextColor);
                    console.log(`setupEditorColorPickers: Color picker created for line ${lineNum}`);
                } catch (error) {
                    console.error(`setupEditorColorPickers: Error creating color picker for line ${lineNum}:`, error);
                    return;
                }

                // Remove existing event listeners to prevent duplicates
                const newColorBtn = colorBtn.cloneNode(true);
                colorBtn.parentNode.replaceChild(newColorBtn, colorBtn);

                // Update the color again after cloning
                newColorBtn.style.backgroundColor = initialColor;
                newColorBtn.id = `editorColorBtn${lineNum}`; // Ensure ID is preserved

                // Color button click - single event listener
                newColorBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    console.log(`Color button clicked for line ${lineNum}`);

                    // Close other dropdowns
                    document.querySelectorAll('.color-dropdown').forEach(dropdown => {
                        if (dropdown !== colorDropdown) {
                            dropdown.classList.remove('show');
                        }
                    });

                    // Toggle current dropdown
                    const isVisible = colorDropdown.classList.contains('show');
                    colorDropdown.classList.toggle('show');
                    console.log(`Color dropdown for line ${lineNum} is now:`, !isVisible ? 'visible' : 'hidden');
                });
            });

            // Remove existing document click listener and add new one
            if (window.editorDocumentClickHandler) {
                document.removeEventListener('click', window.editorDocumentClickHandler);
            }

            window.editorDocumentClickHandler = function(e) {
                if (!e.target.closest('.color-picker-dropdown')) {
                    document.querySelectorAll('.color-dropdown').forEach(dropdown => {
                        dropdown.classList.remove('show');
                    });
                }
            };

            document.addEventListener('click', window.editorDocumentClickHandler);

            // Mark as setup
            window.editorColorPickersSetup = true;
            console.log('Editor color pickers setup completed');
        }

        // Force re-setup of color pickers (useful when DOM changes)
        function forceSetupEditorColorPickers() {
            console.log('forceSetupEditorColorPickers: Forcing re-setup...');
            window.editorColorPickersSetup = false;
            window.editorSettingsButtonsSetup = false;

            // Wait a bit for DOM to be ready
            setTimeout(() => {
                setupEditorColorPickers();
                setupEditorSettingsButtons();
            }, 100);
        }

        // Debug function to test color picker
        window.testColorPicker = function(lineNum) {
            const colorBtn = document.getElementById(`editorColorBtn${lineNum}`);
            const colorDropdown = document.getElementById(`editorColorDropdown${lineNum}`);
            const colorPalette = document.getElementById(`editorColorPalette${lineNum}`);

            console.log(`Testing color picker for line ${lineNum}:`);
            console.log('- Color button:', colorBtn);
            console.log('- Color dropdown:', colorDropdown);
            console.log('- Color palette:', colorPalette);
            console.log('- Dropdown classes:', colorDropdown ? colorDropdown.className : 'N/A');
            console.log('- Palette children:', colorPalette ? colorPalette.children.length : 'N/A');

            if (colorDropdown) {
                colorDropdown.classList.toggle('show');
                console.log('- Toggled dropdown, now visible:', colorDropdown.classList.contains('show'));
            }
        };

        // Setup Editor Settings Buttons
        function setupEditorSettingsButtons() {
            // Prevent multiple setups
            if (window.editorSettingsButtonsSetup) {
                console.log('Editor settings buttons already setup, skipping...');
                return;
            }

            ['1', '2', '3', '4'].forEach(lineNum => {
                const settingsBtn = document.getElementById(`editorSettingsBtn${lineNum}`);
                if (settingsBtn) {
                    // Remove existing event listeners to prevent duplicates
                    const newSettingsBtn = settingsBtn.cloneNode(true);
                    settingsBtn.parentNode.replaceChild(newSettingsBtn, settingsBtn);

                    newSettingsBtn.addEventListener('click', () => {
                        openEditorFontModal(`editorLine${lineNum}`);
                    });
                }
            });

            // Mark as setup
            window.editorSettingsButtonsSetup = true;
            console.log('Editor settings buttons setup completed');
        }

        // Set Editor Text Color
        function setEditorTextColor(lineId, color) {
            // Update local state
            editorTextSettings[lineId].color = color;

            // Update global state
            if (window.editorState && window.editorState.editorTextSettings) {
                window.editorState.editorTextSettings[lineId].color = color;
            }

            const lineNum = lineId.replace('editorLine', '');
            const colorBtn = document.getElementById(`editorColorBtn${lineNum}`);
            if (colorBtn) {
                colorBtn.style.backgroundColor = color;
            }
            updateEditorCanvas();
        }

        // Open Editor Font Modal with comprehensive restoration
        let currentEditorEditingLine = null;
        function openEditorFontModal(lineId) {
            currentEditorEditingLine = lineId;
            const modal = document.getElementById('editorFontModal');

            // Get settings from multiple sources for restoration
            let settings = (window.editorState && window.editorState.editorTextSettings && window.editorState.editorTextSettings[lineId])
                ? window.editorState.editorTextSettings[lineId]
                : editorTextSettings[lineId];

            // Try to restore from saved data if available
            const savedTextSettings = localStorage.getItem('editorTextSettings');
            if (savedTextSettings) {
                try {
                    const parsedSettings = JSON.parse(savedTextSettings);
                    if (parsedSettings[lineId]) {
                        settings = { ...settings, ...parsedSettings[lineId] };
                        // Update global settings
                        editorTextSettings[lineId] = settings;
                        console.log(`openEditorFontModal: Restored settings for ${lineId}:`, settings);
                    }
                } catch (e) {
                    console.warn('openEditorFontModal: Error parsing saved text settings:', e);
                }
            }

            // Set current values in modal
            document.getElementById('editorModalFontSelect').value = settings.fontFamily || 'Arial';
            document.getElementById('editorModalFontSize').value = settings.fontSize || 50;
            document.getElementById('editorModalShadow').checked = settings.shadow !== false;
            document.getElementById('editorModalShadowWidth').value = settings.shadowWidth !== undefined ? settings.shadowWidth : 4;

            modal.style.display = 'flex';
        }

        // Apply Editor Font Settings
        window.applyEditorFontSettings = function() {
            if (!currentEditorEditingLine) return;

            const fontFamily = document.getElementById('editorModalFontSelect').value;
            const fontSize = parseInt(document.getElementById('editorModalFontSize').value);
            const shadow = document.getElementById('editorModalShadow').checked;
            const shadowWidth = parseInt(document.getElementById('editorModalShadowWidth').value);
            const applyToAll = document.getElementById('editorApplyToAll').checked;

            if (applyToAll) {
                // Apply to all editor text lines
                Object.keys(editorTextSettings).forEach(line => {
                    editorTextSettings[line].fontFamily = fontFamily;
                    editorTextSettings[line].fontSize = fontSize;
                    editorTextSettings[line].shadow = shadow;
                    editorTextSettings[line].shadowWidth = shadowWidth;

                    // Update global state
                    if (window.editorState && window.editorState.editorTextSettings && window.editorState.editorTextSettings[line]) {
                        window.editorState.editorTextSettings[line].fontFamily = fontFamily;
                        window.editorState.editorTextSettings[line].fontSize = fontSize;
                        window.editorState.editorTextSettings[line].shadow = shadow;
                        window.editorState.editorTextSettings[line].shadowWidth = shadowWidth;
                    }
                });
            } else {
                // Apply only to current line
                editorTextSettings[currentEditorEditingLine].fontFamily = fontFamily;
                editorTextSettings[currentEditorEditingLine].fontSize = fontSize;
                editorTextSettings[currentEditorEditingLine].shadow = shadow;
                editorTextSettings[currentEditorEditingLine].shadowWidth = shadowWidth;

                // Update global state
                if (window.editorState && window.editorState.editorTextSettings && window.editorState.editorTextSettings[currentEditorEditingLine]) {
                    window.editorState.editorTextSettings[currentEditorEditingLine].fontFamily = fontFamily;
                    window.editorState.editorTextSettings[currentEditorEditingLine].fontSize = fontSize;
                    window.editorState.editorTextSettings[currentEditorEditingLine].shadow = shadow;
                    window.editorState.editorTextSettings[currentEditorEditingLine].shadowWidth = shadowWidth;
                }
            }

            updateEditorCanvas();
            window.closeEditorFontModal();
        };

        // Close Editor Font Modal
        window.closeEditorFontModal = function() {
            const modal = document.getElementById('editorFontModal');
            if (modal) {
                modal.style.display = 'none';
            }
            currentEditorEditingLine = null;
            const applyToAllCheckbox = document.getElementById('editorApplyToAll');
            if (applyToAllCheckbox) {
                applyToAllCheckbox.checked = false;
            }
        };

        // Create Unified Color Picker
        function createUnifiedColorPicker(colorPaletteEl, colorDropdown, lineId, setColorFunction) {
            // Clear existing content
            colorPaletteEl.innerHTML = '';

            // Create color palette
            colorPalette.forEach(color => {
                const swatch = document.createElement('div');
                swatch.className = 'color-swatch';

                if (color === '✓') {
                    swatch.classList.add('checkmark');
                    swatch.innerHTML = '✓';
                } else {
                    swatch.style.backgroundColor = color;
                }

                swatch.addEventListener('click', () => {
                    if (color !== '✓') {
                        setColorFunction(lineId, color);
                        colorDropdown.classList.remove('show');

                        // Remove selected class from all swatches
                        colorPaletteEl.querySelectorAll('.color-swatch').forEach(s => s.classList.remove('selected'));
                        // Add selected class to clicked swatch
                        swatch.classList.add('selected');
                    }
                });
                colorPaletteEl.appendChild(swatch);
            });

            // Create advanced color picker
            const advancedPicker = document.createElement('div');
            advancedPicker.className = 'color-picker-advanced';

            // Color gradient area
            const gradientArea = document.createElement('div');
            gradientArea.className = 'color-gradient-area';
            gradientArea.style.background = 'linear-gradient(to right, #fff, #ff0000)';

            const gradientPicker = document.createElement('div');
            gradientPicker.className = 'color-gradient-picker';
            gradientPicker.style.left = '50%';
            gradientPicker.style.top = '50%';
            gradientArea.appendChild(gradientPicker);

            // Hue slider
            const hueContainer = document.createElement('div');
            hueContainer.className = 'hue-slider-container';

            const hueSlider = document.createElement('div');
            hueSlider.className = 'hue-slider';

            const hueHandle = document.createElement('div');
            hueHandle.className = 'hue-slider-handle';
            hueHandle.style.top = '0%';
            hueSlider.appendChild(hueHandle);

            // Color preview and buttons
            const previewSection = document.createElement('div');
            previewSection.className = 'color-preview-section';

            const colorPreview = document.createElement('div');
            colorPreview.className = 'color-preview-large';
            colorPreview.style.backgroundColor = '#ff0000';

            const buttonsContainer = document.createElement('div');
            buttonsContainer.className = 'color-picker-buttons';

            const cancelBtn = document.createElement('button');
            cancelBtn.className = 'color-picker-btn-small cancel';
            cancelBtn.textContent = 'cancel';
            cancelBtn.addEventListener('click', () => {
                colorDropdown.classList.remove('show');
            });

            const chooseBtn = document.createElement('button');
            chooseBtn.className = 'color-picker-btn-small choose';
            chooseBtn.textContent = 'choose';
            chooseBtn.addEventListener('click', () => {
                const selectedColor = colorPreview.style.backgroundColor;
                setColorFunction(lineId, selectedColor);
                colorDropdown.classList.remove('show');
            });

            buttonsContainer.appendChild(cancelBtn);
            buttonsContainer.appendChild(chooseBtn);
            previewSection.appendChild(colorPreview);
            previewSection.appendChild(buttonsContainer);

            hueContainer.appendChild(hueSlider);
            advancedPicker.appendChild(gradientArea);
            advancedPicker.appendChild(hueContainer);
            advancedPicker.appendChild(previewSection);

            colorDropdown.appendChild(advancedPicker);

            // Add interaction handlers for advanced picker
            setupAdvancedColorPicker(gradientArea, hueSlider, colorPreview, lineId);
        }

        // Setup Advanced Color Picker Interactions
        function setupAdvancedColorPicker(gradientArea, hueSlider, colorPreview, lineId) {
            let currentHue = 0;
            let currentSaturation = 100;
            let currentLightness = 50;

            // Convert HSL to RGB
            function hslToRgb(h, s, l) {
                h /= 360;
                s /= 100;
                l /= 100;

                const a = s * Math.min(l, 1 - l);
                const f = n => {
                    const k = (n + h * 12) % 12;
                    return l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
                };

                return [Math.round(f(0) * 255), Math.round(f(8) * 255), Math.round(f(4) * 255)];
            }

            // Convert RGB to Hex
            function rgbToHex(r, g, b) {
                return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
            }

            // Update color preview
            function updateColorPreview() {
                const [r, g, b] = hslToRgb(currentHue, currentSaturation, currentLightness);
                const hexColor = rgbToHex(r, g, b);
                colorPreview.style.backgroundColor = hexColor;
            }

            // Update gradient background based on hue
            function updateGradientBackground() {
                const [r, g, b] = hslToRgb(currentHue, 100, 50);
                const hueColor = rgbToHex(r, g, b);
                gradientArea.style.background = `linear-gradient(to right, #fff, ${hueColor})`;
            }

            // Hue slider interaction
            hueSlider.addEventListener('mousedown', function(e) {
                const rect = hueSlider.getBoundingClientRect();
                const y = e.clientY - rect.top;
                const percentage = Math.max(0, Math.min(100, (y / rect.height) * 100));

                currentHue = (percentage / 100) * 360;
                const handle = hueSlider.querySelector('.hue-slider-handle');
                handle.style.top = percentage + '%';

                updateGradientBackground();
                updateColorPreview();
            });

            // Gradient area interaction
            gradientArea.addEventListener('mousedown', function(e) {
                const rect = gradientArea.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                const saturation = Math.max(0, Math.min(100, (x / rect.width) * 100));
                const lightness = Math.max(0, Math.min(100, 100 - (y / rect.height) * 100));

                currentSaturation = saturation;
                currentLightness = lightness;

                const picker = gradientArea.querySelector('.color-gradient-picker');
                picker.style.left = (x / rect.width) * 100 + '%';
                picker.style.top = (y / rect.height) * 100 + '%';

                updateColorPreview();
            });

            // Initialize
            updateGradientBackground();
            updateColorPreview();
        }

        // Initialize anniversary and benefit templates when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                initAnniversaryTemplates();
                initBenefitTemplates();
            });
        } else {
            initAnniversaryTemplates();
            initBenefitTemplates();
        }

        // Initialize the billboard
        init();
    });

    // Global error handler for better debugging
    window.handleGlobalError = function(error, context = 'Unknown') {
        console.error(`Global Error in ${context}:`, error);
        console.error('Error stack:', error.stack);

        // Store error for debugging
        if (!window.errorLog) window.errorLog = [];
        window.errorLog.push({
            timestamp: new Date().toISOString(),
            context: context,
            error: error.message,
            stack: error.stack
        });

        // Keep only last 10 errors
        if (window.errorLog.length > 10) {
            window.errorLog = window.errorLog.slice(-10);
        }
    };

    // Performance monitoring
    window.performanceMonitor = {
        start: function(operation) {
            this[operation + '_start'] = performance.now();
        },
        end: function(operation) {
            const startTime = this[operation + '_start'];
            if (startTime) {
                const duration = performance.now() - startTime;
                console.log(`Performance: ${operation} took ${duration.toFixed(2)}ms`);
                delete this[operation + '_start'];
            }
        }
    };

    // Global initialization function to ensure everything is properly set up
    window.initializeGlobalFunctions = function() {
        console.log('Initializing global functions...');
        window.performanceMonitor.start('initialization');

        try {
            // Ensure global state exists
            if (!window.editorState) {
                window.editorState = {
                    currentAnniversaryTemplate: null,
                    selectedAnniversaryBackground: 'Anniversary-1',
                    selectedBackgroundType: 'image',
                    selectedBackgroundColor: '#ff0000',
                    editorCanvas: null,
                    editorCtx: null,
                    editorUploadedImage: null,
                    isEditorMode: false,
                    anniversaryTemplates: null,
                    editorTextSettings: {
                        editorLine1: { color: '#ffffff', fontSize: 50, fontFamily: 'Arial', shadow: true, shadowWidth: 4 },
                        editorLine2: { color: '#ffffff', fontSize: 50, fontFamily: 'Arial', shadow: true, shadowWidth: 4 },
                        editorLine3: { color: '#ffffff', fontSize: 50, fontFamily: 'Arial', shadow: true, shadowWidth: 4 },
                        editorLine4: { color: '#ffffff', fontSize: 50, fontFamily: 'Arial', shadow: true, shadowWidth: 4 }
                    }
                };
                console.log('Global state initialized');
            }

            // Initialize canvas references
            const editorCanvas = document.getElementById('editorCanvas');
            if (editorCanvas && !window.editorState.editorCanvas) {
                window.editorState.editorCanvas = editorCanvas;
                window.editorState.editorCtx = editorCanvas.getContext('2d');
                console.log('Global canvas references initialized');
            }

            // Initialize function flags
            window.confirmTemplateDesignGlobal.isRunning = false;
            window.backToTemplateSelection.isRunning = false;

            // Create global reset function
            window.resetAllFields = function() {
                console.log('Resetting all fields and state...');

                // Reset text input fields
                const textInputs = ['line1', 'line2', 'line3', 'line4', 'editorLine1', 'editorLine2', 'editorLine3', 'editorLine4'];
                textInputs.forEach(inputId => {
                    const input = document.getElementById(inputId);
                    if (input) {
                        input.value = '';
                    }
                });

                // Reset color buttons to default white
                const colorButtons = ['colorBtn1', 'colorBtn2', 'colorBtn3', 'colorBtn4', 'editorColorBtn1', 'editorColorBtn2', 'editorColorBtn3', 'editorColorBtn4'];
                colorButtons.forEach(btnId => {
                    const btn = document.getElementById(btnId);
                    if (btn) {
                        btn.style.backgroundColor = '#ffffff';
                    }
                });

                // Reset text settings to defaults
                const defaultTextSettings = { color: '#ffffff', fontSize: 50, fontFamily: 'Arial', shadow: true, shadowWidth: 4 };
                if (window.textSettings) {
                    Object.keys(window.textSettings).forEach(line => {
                        window.textSettings[line] = { ...defaultTextSettings };
                    });
                }
                if (window.editorTextSettings) {
                    Object.keys(window.editorTextSettings).forEach(line => {
                        window.editorTextSettings[line] = { ...defaultTextSettings };
                    });
                }

                // Reset global editor state
                if (window.editorState) {
                    window.editorState.editorTextSettings = {
                        editorLine1: { ...defaultTextSettings },
                        editorLine2: { ...defaultTextSettings },
                        editorLine3: { ...defaultTextSettings },
                        editorLine4: { ...defaultTextSettings }
                    };
                    window.editorState.editorUploadedImage = null;
                    window.editorState.selectedBackgroundColor = '#000000';
                    window.editorState.selectedAnniversaryBackground = 'Anniversary-1';
                    window.editorState.selectedBackgroundType = 'image';
                }

                // Reset uploaded images
                if (window.uploadedImage) window.uploadedImage = null;
                if (window.editorUploadedImage) window.editorUploadedImage = null;

                // Reset background settings
                if (window.selectedBackgroundColor) window.selectedBackgroundColor = '#000000';
                if (window.selectedAnniversaryBackground) window.selectedAnniversaryBackground = 'Anniversary-1';
                if (window.selectedBackgroundType) window.selectedBackgroundType = 'image';

                // Clear template selections
                document.querySelectorAll('.template-card, .anniversary-template-card').forEach(card => {
                    card.classList.remove('selected');
                });

                // Reset canvas if it exists
                const canvas = document.getElementById('billboardCanvas');
                const editorCanvas = document.getElementById('editorCanvas');
                [canvas, editorCanvas].forEach(canvasEl => {
                    if (canvasEl) {
                        const ctx = canvasEl.getContext('2d');
                        ctx.clearRect(0, 0, canvasEl.width, canvasEl.height);
                        ctx.fillStyle = '#000000';
                        ctx.fillRect(0, 0, canvasEl.width, canvasEl.height);
                    }
                });

                // Update canvas display
                if (window.updateBillboard) {
                    window.updateBillboard();
                }
                if (window.updateEditorCanvas) {
                    window.updateEditorCanvas();
                }
                if (window.updateUniversalEditorCanvas) {
                    window.updateUniversalEditorCanvas();
                }

                console.log('All fields and state reset successfully');

                // Show confirmation message
                const confirmationDiv = document.createElement('div');
                confirmationDiv.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: linear-gradient(135deg, #28a745, #20c997);
                    color: white;
                    padding: 15px 25px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
                    z-index: 10000;
                    font-family: Arial, sans-serif;
                    font-size: 14px;
                    font-weight: 600;
                    animation: slideInRight 0.5s ease-out;
                `;

                confirmationDiv.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <span style="font-size: 18px;">🔄</span>
                        <div>All fields reset successfully!</div>
                    </div>
                `;

                document.body.appendChild(confirmationDiv);

                // Remove the message after 3 seconds
                setTimeout(() => {
                    if (confirmationDiv.parentNode) {
                        confirmationDiv.remove();
                    }
                }, 3000);
            };

            console.log('Global functions initialized successfully');
            window.performanceMonitor.end('initialization');

        } catch (error) {
            window.handleGlobalError(error, 'initializeGlobalFunctions');
        }
    };

    // Global error handling
    window.addEventListener('error', function(event) {
        window.handleGlobalError(event.error, 'Global Window Error');
    });

    window.addEventListener('unhandledrejection', function(event) {
        window.handleGlobalError(event.reason, 'Unhandled Promise Rejection');
    });

    // Professional page initialization with state restoration
    function initializePage() {
        console.log('initializePage: Starting page initialization...');

        // Initialize global functions first
        window.initializeGlobalFunctions();

        // Check if we're returning from checkout
        const returningFromCheckout = localStorage.getItem('returningFromCheckout');
        if (returningFromCheckout) {
            console.log('initializePage: Detected return from checkout, restoring design state...');

            // Remove the flag
            localStorage.removeItem('returningFromCheckout');

            // Professional restoration with editor reopening
            setTimeout(() => {
                const savedDesign = localStorage.getItem('adDesignData');
                const currentPurpose = localStorage.getItem('selectedPurpose');

                if (savedDesign && currentPurpose === 'Anniversary') {
                    console.log('initializePage: Restoring anniversary design with editor...');

                    // Show anniversary templates section first
                    const anniversarySection = document.getElementById('anniversaryTemplatesSection');
                    if (anniversarySection) {
                        anniversarySection.style.display = 'block';
                    }
                } else if (savedDesign && currentPurpose === 'Benefit') {
                    console.log('initializePage: Restoring benefit design with editor...');

                    // Show benefit templates section first
                    const benefitSection = document.getElementById('benefitTemplatesSection');
                    if (benefitSection) {
                        benefitSection.style.display = 'block';
                    }
                }

                if (savedDesign && (currentPurpose === 'Anniversary' || currentPurpose === 'Benefit')) {
                    // Open the editor container - ALWAYS keep it open when returning from checkout
                    const editorContainer = document.getElementById('templateEditorContainer');
                    if (editorContainer) {
                        editorContainer.classList.add('active');
                        console.log('initializePage: Editor container reopened');

                        // Set flag to ensure editor stays open
                        localStorage.setItem('editorShouldStayOpen', 'true');
                    }

                    // Restore design state
                    const restored = window.restoreBillboardDesign();
                    if (restored) {
                        console.log('initializePage: Design state restored successfully');

                        // Update editor title to show confirmed status (design was confirmed before checkout)
                        updateEditorTitle('confirmed');

                        // Restore complete UI state
                        setTimeout(() => {
                            if (window.restoreCompleteUIState) {
                                window.restoreCompleteUIState();
                                console.log('initializePage: Complete UI state restored');
                            }
                        }, 800);

                        // Try to update canvas if function exists
                        setTimeout(() => {
                            if (window.updateEditorCanvasGlobal) {
                                window.updateEditorCanvasGlobal();
                                console.log('initializePage: Editor canvas updated after restoration');
                            } else if (window.updateCanvas) {
                                window.updateCanvas();
                                console.log('initializePage: Canvas updated after restoration');
                            }
                        }, 1200);

                        // Ensure editor remains open after all restoration
                        setTimeout(() => {
                            if (editorContainer && !editorContainer.classList.contains('active')) {
                                editorContainer.classList.add('active');
                                console.log('initializePage: Re-ensured editor container is open');
                            }
                        }, 1500);
                    }
                } else {
                    // Regular restoration for non-anniversary templates
                    const restored = window.restoreBillboardDesign();
                    if (restored) {
                        console.log('initializePage: Design state restored successfully');

                        setTimeout(() => {
                            if (window.updateCanvas) {
                                window.updateCanvas();
                                console.log('initializePage: Canvas updated after restoration');
                            } else if (window.updateBillboardCanvas) {
                                window.updateBillboardCanvas();
                                console.log('initializePage: Billboard canvas updated after restoration');
                            }
                        }, 500);
                    }
                }
            }, 1000);
        } else {
            console.log('initializePage: Normal page load, checking for existing design data...');

            // Check for existing design data and restore if available
            setTimeout(() => {
                const hasDesignData = localStorage.getItem('adDesignData');
                if (hasDesignData) {
                    console.log('initializePage: Found existing design data, attempting restoration...');
                    window.restoreBillboardDesign();
                }
            }, 2000);
        }
    }

    // Call initialization when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializePage);
    } else {
        initializePage();
    }

    // Debug helper function
    window.debugBillboardSystem = function() {
        console.log('=== Billboard System Debug Info ===');
        console.log('Editor State:', window.editorState);
        console.log('Error Log:', window.errorLog);
        console.log('Function States:', {
            confirmTemplateDesignRunning: window.confirmTemplateDesignGlobal?.isRunning,
            backToTemplateSelectionRunning: window.backToTemplateSelection?.isRunning
        });
        console.log('DOM Elements:', {
            editorCanvas: !!document.getElementById('editorCanvas'),
            confirmBtn: !!document.getElementById('confirmTemplateBtn'),
            backBtn: !!document.getElementById('backToTemplatesBtn'),
            editorContainer: !!document.getElementById('templateEditorContainer')
        });
        console.log('LocalStorage:', {
            selectedPurpose: localStorage.getItem('selectedPurpose'),
            adDesignData: localStorage.getItem('adDesignData'),
            adPreviewImage: !!localStorage.getItem('adPreviewImage')
        });
        console.log('=== End Debug Info ===');
    };

    // Comprehensive restoration test function
    window.testCompleteRestoration = function() {
        console.log('=== TESTING COMPLETE RESTORATION FLOW ===');

        try {
            // Test 1: Check if saved data exists
            const savedData = localStorage.getItem('adDesignData');
            if (!savedData) {
                console.error('TEST FAILED: No saved design data found');
                return false;
            }

            const designData = JSON.parse(savedData);
            console.log('✓ Test 1 PASSED: Saved design data found:', designData);

            // Test 2: Check text restoration
            let textRestored = true;
            if (designData.textLines) {
                ['editorLine1', 'editorLine2', 'editorLine3', 'editorLine4'].forEach((editorId, index) => {
                    const lineKey = `line${index + 1}`;
                    const element = document.getElementById(editorId);
                    if (element && designData.textLines[lineKey]) {
                        if (element.value !== designData.textLines[lineKey]) {
                            console.error(`TEST FAILED: Text not restored for ${editorId}. Expected: "${designData.textLines[lineKey]}", Got: "${element.value}"`);
                            textRestored = false;
                        }
                    } else if (designData.textLines[lineKey]) {
                        console.error(`TEST FAILED: Element ${editorId} not found or no saved text for ${lineKey}`);
                        textRestored = false;
                    }
                });
            }

            if (textRestored) {
                console.log('✓ Test 2 PASSED: Text content restored correctly');
            }

            // Test 3: Check color picker restoration
            let colorsRestored = true;
            if (designData.uiState && designData.uiState.colorPickerStates) {
                Object.keys(designData.uiState.colorPickerStates).forEach(btnId => {
                    const btn = document.getElementById(btnId);
                    if (btn) {
                        const expectedColor = designData.uiState.colorPickerStates[btnId];
                        const actualColor = btn.style.backgroundColor;
                        if (actualColor !== expectedColor) {
                            console.error(`TEST FAILED: Color picker ${btnId} not restored. Expected: "${expectedColor}", Got: "${actualColor}"`);
                            colorsRestored = false;
                        }
                    } else {
                        console.error(`TEST FAILED: Color picker button ${btnId} not found`);
                        colorsRestored = false;
                    }
                });
            }

            if (colorsRestored) {
                console.log('✓ Test 3 PASSED: Color picker states restored correctly');
            }

            // Test 4: Check template selection restoration
            let templateRestored = true;
            if (designData.template) {
                const templateCards = document.querySelectorAll('.anniversary-template-card');
                let foundSelected = false;
                templateCards.forEach(card => {
                    if (card.dataset.template === designData.template && card.classList.contains('selected')) {
                        foundSelected = true;
                    }
                });
                if (!foundSelected) {
                    console.error(`TEST FAILED: Template ${designData.template} not visually selected`);
                    templateRestored = false;
                }
            }

            if (templateRestored) {
                console.log('✓ Test 4 PASSED: Template selection restored correctly');
            }

            // Test 5: Check editor state restoration
            let editorStateRestored = true;
            if (window.editorState) {
                if (window.editorState.currentAnniversaryTemplate !== designData.template) {
                    console.error(`TEST FAILED: Editor state template not restored. Expected: "${designData.template}", Got: "${window.editorState.currentAnniversaryTemplate}"`);
                    editorStateRestored = false;
                }
                if (window.editorState.selectedAnniversaryBackground !== designData.bgGraphic) {
                    console.error(`TEST FAILED: Editor state background not restored. Expected: "${designData.bgGraphic}", Got: "${window.editorState.selectedAnniversaryBackground}"`);
                    editorStateRestored = false;
                }
            } else {
                console.error('TEST FAILED: Editor state not found');
                editorStateRestored = false;
            }

            if (editorStateRestored) {
                console.log('✓ Test 5 PASSED: Editor state restored correctly');
            }

            // Test 6: Check uploaded image restoration
            let imageRestored = true;
            if (designData.uploadedImageData) {
                if (!window.editorState?.editorUploadedImage) {
                    console.error('TEST FAILED: Uploaded image not restored to editor state');
                    imageRestored = false;
                }
            }

            if (imageRestored) {
                console.log('✓ Test 6 PASSED: Uploaded image restored correctly');
            }

            // Overall test result
            const allTestsPassed = textRestored && colorsRestored && templateRestored && editorStateRestored && imageRestored;

            if (allTestsPassed) {
                console.log('🎉 ALL TESTS PASSED: Complete restoration working correctly!');
                return true;
            } else {
                console.error('❌ SOME TESTS FAILED: Restoration needs fixes');
                return false;
            }

        } catch (error) {
            console.error('TEST ERROR: Exception during restoration test:', error);
            return false;
        }

        console.log('=== END RESTORATION TEST ===');
    };

    // Simulate checkout return flow for testing
    window.simulateCheckoutReturn = function() {
        console.log('=== SIMULATING CHECKOUT RETURN FLOW ===');

        try {
            // First, save current state (simulate going to checkout)
            if (window.saveBillboardData) {
                window.saveBillboardData();
                console.log('✓ Current state saved');
            }

            // Set the return flag (simulate coming back from checkout)
            localStorage.setItem('returningFromCheckout', 'true');
            console.log('✓ Return flag set');

            // Clear current UI state (simulate page reload)
            const editorContainer = document.getElementById('templateEditorContainer');
            if (editorContainer) {
                editorContainer.classList.remove('active');
            }

            // Clear template selections
            document.querySelectorAll('.anniversary-template-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Clear color picker states
            ['editorColorBtn1', 'editorColorBtn2', 'editorColorBtn3', 'editorColorBtn4'].forEach(btnId => {
                const btn = document.getElementById(btnId);
                if (btn) {
                    btn.style.backgroundColor = '#ffffff';
                }
            });

            console.log('✓ UI state cleared (simulating page reload)');

            // Now trigger the restoration (simulate page initialization)
            setTimeout(() => {
                console.log('🔄 Triggering restoration...');

                // Simulate the restoration flow
                const savedDesign = localStorage.getItem('adDesignData');
                const currentPurpose = localStorage.getItem('selectedPurpose');

                if (savedDesign && currentPurpose === 'Anniversary') {
                    // Show anniversary templates section
                    const anniversarySection = document.getElementById('anniversaryTemplatesSection');
                    if (anniversarySection) {
                        anniversarySection.style.display = 'block';
                    }
                } else if (savedDesign && currentPurpose === 'Benefit') {
                    // Show benefit templates section
                    const benefitSection = document.getElementById('benefitTemplatesSection');
                    if (benefitSection) {
                        benefitSection.style.display = 'block';
                    }
                }

                if (savedDesign && (currentPurpose === 'Anniversary' || currentPurpose === 'Benefit')) {

                    // Open the editor container
                    if (editorContainer) {
                        editorContainer.classList.add('active');
                        console.log('✓ Editor container reopened');
                    }

                    // Restore design state
                    const restored = window.restoreBillboardDesign();
                    if (restored) {
                        console.log('✓ Design state restored');

                        // Restore complete UI state
                        setTimeout(() => {
                            if (window.restoreCompleteUIState) {
                                window.restoreCompleteUIState();
                                console.log('✓ Complete UI state restored');
                            }

                            // Update canvas
                            setTimeout(() => {
                                if (window.updateEditorCanvasGlobal) {
                                    window.updateEditorCanvasGlobal();
                                    console.log('✓ Canvas updated');
                                }

                                // Run the test
                                setTimeout(() => {
                                    console.log('🧪 Running restoration test...');
                                    const testResult = window.testCompleteRestoration();

                                    if (testResult) {
                                        console.log('🎉 SIMULATION SUCCESSFUL: Checkout return flow working correctly!');
                                    } else {
                                        console.error('❌ SIMULATION FAILED: Issues found in restoration flow');
                                    }

                                    // Clean up
                                    localStorage.removeItem('returningFromCheckout');
                                    console.log('=== END SIMULATION ===');
                                }, 500);
                            }, 500);
                        }, 300);
                    }
                }
            }, 1000);

        } catch (error) {
            console.error('SIMULATION ERROR:', error);
            localStorage.removeItem('returningFromCheckout');
        }
    };

    // Professional editor state management
    window.ensureEditorOpen = function() {
        const currentPurpose = localStorage.getItem('selectedPurpose');
        const hasDesignData = localStorage.getItem('adDesignData');

        if (currentPurpose === 'Anniversary' && hasDesignData) {
            console.log('ensureEditorOpen: Opening anniversary editor...');

            // Show anniversary templates section
            const anniversarySection = document.getElementById('anniversaryTemplatesSection');
            if (anniversarySection) {
                anniversarySection.style.display = 'block';
            }

            // Open editor container
            const editorContainer = document.getElementById('templateEditorContainer');
            if (editorContainer) {
                editorContainer.classList.add('active');
                console.log('ensureEditorOpen: Editor container opened');
            }

            // Update title to show confirmed status
            setTimeout(() => {
                updateEditorTitle('confirmed');
            }, 500);

            return true;
        }

        if (currentPurpose === 'Benefit' && hasDesignData) {
            console.log('ensureEditorOpen: Opening benefit editor...');

            // Show benefit templates section
            const benefitSection = document.getElementById('benefitTemplatesSection');
            if (benefitSection) {
                benefitSection.style.display = 'block';
            }

            // Open editor container
            const editorContainer = document.getElementById('templateEditorContainer');
            if (editorContainer) {
                editorContainer.classList.add('active');
                console.log('ensureEditorOpen: Editor container opened');
            }

            // Update title to show confirmed status
            setTimeout(() => {
                updateEditorTitle('confirmed');
            }, 500);

            return true;
        }

        return false;
    };

    // Enhanced template selection to maintain editor state
    window.selectAnniversaryTemplateEnhanced = function(templateName) {
        console.log('selectAnniversaryTemplateEnhanced: Selecting template:', templateName);

        // Call original function if it exists
        if (window.selectAnniversaryTemplate) {
            window.selectAnniversaryTemplate(templateName);
        }

        // Ensure editor stays open after template selection
        setTimeout(() => {
            const editorContainer = document.getElementById('templateEditorContainer');
            if (editorContainer && !editorContainer.classList.contains('active')) {
                editorContainer.classList.add('active');
                console.log('selectAnniversaryTemplateEnhanced: Re-opened editor after template selection');
            }
        }, 100);
    };

    // Global function for template reset
    window.showAllTemplates = function() {
        // Close any open editor
        const editorContainer = document.getElementById('templateEditorContainer');
        if (editorContainer && editorContainer.classList.contains('active')) {
            backToTemplateSelection();
        }

        // Close any open modals
        const fontModal = document.getElementById('editorFontModal');
        if (fontModal && fontModal.style.display === 'flex') {
            window.closeEditorFontModal();
        }

        const bgDialog = document.getElementById('backgroundGraphicDialog');
        if (bgDialog && bgDialog.classList.contains('show')) {
            bgDialog.classList.remove('show');
        }

        // Reset template selections
        currentAnniversaryTemplate = null;
        currentBenefitTemplate = null;
        editorUploadedImage = null;
        syncEditorState();
        document.querySelectorAll('.anniversary-template-card, .benefit-template-card').forEach(card => {
            card.classList.remove('selected');
        });

        // Reset to show all templates
        const purposeDisplay = document.getElementById('purposeDisplay');
        if (purposeDisplay) {
            purposeDisplay.style.display = 'none';
        }
        localStorage.removeItem('selectedPurpose');
        const selectedPurposeInput = document.getElementById('selectedPurpose');
        if (selectedPurposeInput) {
            selectedPurposeInput.value = '';
        }

        // Reset current purpose
        currentPurpose = null;

        // Show default template state
        showDefaultTemplateState();

        console.log('Templates reset to default state');
    };

    // Image Resize Dialog Functions
    let originalImageForResize = null;
    let cropData = { x: 50, y: 50, width: 200, height: 200 };
    let isDragging = false;
    let isResizing = false;
    let dragStart = { x: 0, y: 0 };
    let resizeHandle = null;
    let imageContainer = null;

    // Global mouse event handlers for image resize
    function handleMouseMove(e) {
        const canvas = document.getElementById('imageResizeCanvas');
        if (!canvas) return;

        const rect = canvas.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        if (isDragging) {
            let newX = mouseX - dragStart.x;
            let newY = mouseY - dragStart.y;

            // Keep within bounds
            newX = Math.max(0, Math.min(newX, canvas.offsetWidth - cropData.width));
            newY = Math.max(0, Math.min(newY, canvas.offsetHeight - cropData.height));

            cropData.x = newX;
            cropData.y = newY;
            updateCropOverlay();
        } else if (isResizing && resizeHandle) {
            const deltaX = mouseX - dragStart.x;
            const deltaY = mouseY - dragStart.y;

            // For 1:1 aspect ratio, use the larger absolute delta
            const delta = Math.max(Math.abs(deltaX), Math.abs(deltaY));
            const signX = deltaX >= 0 ? 1 : -1;
            const signY = deltaY >= 0 ? 1 : -1;

            let newWidth = cropData.width;
            let newHeight = cropData.height;
            let newX = cropData.x;
            let newY = cropData.y;

            if (resizeHandle === 'se') {
                newWidth = cropData.width + delta * signX;
                newHeight = newWidth; // 1:1 aspect ratio
            } else if (resizeHandle === 'nw') {
                newWidth = cropData.width - delta * signX;
                newHeight = newWidth; // 1:1 aspect ratio
                newX = cropData.x + (cropData.width - newWidth);
                newY = cropData.y + (cropData.height - newHeight);
            } else if (resizeHandle === 'ne') {
                newWidth = cropData.width + delta * signX;
                newHeight = newWidth; // 1:1 aspect ratio
                newY = cropData.y + (cropData.height - newHeight);
            } else if (resizeHandle === 'sw') {
                newWidth = cropData.width - delta * signX;
                newHeight = newWidth; // 1:1 aspect ratio
                newX = cropData.x + (cropData.width - newWidth);
            }

            // Ensure minimum size and bounds
            if (newWidth >= 50 && newHeight >= 50 &&
                newX >= 0 && newY >= 0 &&
                newX + newWidth <= canvas.offsetWidth &&
                newY + newHeight <= canvas.offsetHeight) {
                cropData.x = newX;
                cropData.y = newY;
                cropData.width = newWidth;
                cropData.height = newHeight;
                updateCropOverlay();
            }
        }
    }

    // Mouse up handler
    function handleMouseUp() {
        if (isDragging || isResizing) {
            console.log('Ending drag/resize');
        }
        isDragging = false;
        isResizing = false;
        resizeHandle = null;
    }

    function showImageResizeDialog(img) {
        console.log('Showing image resize dialog');
        originalImageForResize = img;
        const modal = document.getElementById('imageResizeModal');
        const resizeImage = document.getElementById('resizeImage');
        const canvas = document.getElementById('imageResizeCanvas');

        // Clean up any existing event listeners first
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);

        // Set up the image
        resizeImage.src = img.src;
        resizeImage.onload = function() {
            console.log('Image loaded:', img.width, 'x', img.height);

            // Calculate display size
            const maxWidth = 600;
            const maxHeight = 400;
            let displayWidth = img.width;
            let displayHeight = img.height;

            // Scale to fit
            if (displayWidth > maxWidth || displayHeight > maxHeight) {
                const scaleX = maxWidth / displayWidth;
                const scaleY = maxHeight / displayHeight;
                const scale = Math.min(scaleX, scaleY);

                displayWidth = Math.floor(displayWidth * scale);
                displayHeight = Math.floor(displayHeight * scale);
            }

            // Set image size
            resizeImage.style.width = displayWidth + 'px';
            resizeImage.style.height = displayHeight + 'px';

            // Set canvas size to match image
            canvas.style.width = displayWidth + 'px';
            canvas.style.height = displayHeight + 'px';

            // Initialize crop overlay (1:1 aspect ratio, centered)
            const cropSize = Math.min(displayWidth, displayHeight) * 0.5;
            cropData = {
                x: (displayWidth - cropSize) / 2,
                y: (displayHeight - cropSize) / 2,
                width: cropSize,
                height: cropSize
            };

            console.log('Crop data initialized:', cropData);
            updateCropOverlay();
            setupCropInteractions();

            // Add global event listeners for mouse interactions
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
        };

        modal.style.display = 'flex';

        // Ensure modal is visible and on top
        modal.style.zIndex = '2000';
        modal.style.position = 'fixed';
    }

    function updateCropOverlay() {
        const overlay = document.getElementById('cropOverlay');
        if (overlay) {
            overlay.style.left = cropData.x + 'px';
            overlay.style.top = cropData.y + 'px';
            overlay.style.width = cropData.width + 'px';
            overlay.style.height = cropData.height + 'px';
            console.log('Overlay updated:', cropData);
        }
    }

    function setupCropInteractions() {
        const overlay = document.getElementById('cropOverlay');
        const canvas = document.getElementById('imageResizeCanvas');

        if (!overlay || !canvas) {
            console.error('Overlay or canvas not found');
            return;
        }

        // Remove existing listeners
        overlay.removeEventListener('mousedown', handleOverlayMouseDown);

        // Make overlay draggable
        function handleOverlayMouseDown(e) {
            if (e.target.classList.contains('crop-handle')) return;

            console.log('Starting drag');
            isDragging = true;
            const rect = canvas.getBoundingClientRect();
            dragStart.x = e.clientX - rect.left - cropData.x;
            dragStart.y = e.clientY - rect.top - cropData.y;
            e.preventDefault();
        }

        overlay.addEventListener('mousedown', handleOverlayMouseDown);

        // Handle resize handles
        const handles = overlay.querySelectorAll('.crop-handle');
        handles.forEach(handle => {
            handle.addEventListener('mousedown', function(e) {
                console.log('Starting resize with handle:', handle.classList[1]);
                isResizing = true;
                resizeHandle = handle.classList[1]; // nw, ne, sw, se
                const rect = canvas.getBoundingClientRect();
                dragStart.x = e.clientX - rect.left;
                dragStart.y = e.clientY - rect.top;
                e.preventDefault();
                e.stopPropagation();
            });
        });
    }

    window.cancelImageResize = function() {
        console.log('Canceling image resize');
        const modal = document.getElementById('imageResizeModal');
        modal.style.display = 'none';
        originalImageForResize = null;

        // Clean up event listeners
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
    };

    window.confirmImageResize = function() {
        console.log('Confirming image resize');
        if (!originalImageForResize) {
            console.error('No original image found');
            return;
        }

        try {
            // Create a canvas to crop the image
            const cropCanvas = document.createElement('canvas');
            const cropCtx = cropCanvas.getContext('2d');

            // Set canvas size to crop dimensions (1:1 aspect ratio)
            const finalSize = 400; // Final output size
            cropCanvas.width = finalSize;
            cropCanvas.height = finalSize;

            // Calculate scale factor between display and actual image
            const resizeImage = document.getElementById('resizeImage');
            const scaleX = originalImageForResize.width / resizeImage.offsetWidth;
            const scaleY = originalImageForResize.height / resizeImage.offsetHeight;

            console.log('Scale factors:', scaleX, scaleY);
            console.log('Crop data:', cropData);

            // Calculate actual crop coordinates
            const actualCropX = cropData.x * scaleX;
            const actualCropY = cropData.y * scaleY;
            const actualCropWidth = cropData.width * scaleX;
            const actualCropHeight = cropData.height * scaleY;

            console.log('Actual crop:', actualCropX, actualCropY, actualCropWidth, actualCropHeight);

            // Draw the cropped image
            cropCtx.drawImage(
                originalImageForResize,
                actualCropX, actualCropY, actualCropWidth, actualCropHeight,
                0, 0, finalSize, finalSize
            );

            // Convert to image and set as uploaded image
            const croppedImage = new Image();
            croppedImage.onload = function() {
                console.log('Cropped image loaded successfully');

                // Set the cropped image in global state
                if (window.editorState) {
                    window.editorState.editorUploadedImage = croppedImage;
                    console.log('Cropped image set in global state');
                }

                // Force canvas update with a slight delay to ensure proper rendering
                setTimeout(() => {
                    if (window.updateEditorCanvasGlobal) {
                        window.updateEditorCanvasGlobal();
                        console.log('Canvas updated with cropped image via global function');
                    } else if (window.updateEditorCanvas) {
                        window.updateEditorCanvas();
                        console.log('Canvas updated with cropped image via local function');
                    } else {
                        console.error('No updateEditorCanvas function available');
                    }
                }, 100);

                // Close modal
                const modal = document.getElementById('imageResizeModal');
                modal.style.display = 'none';
                originalImageForResize = null;

                // Clean up event listeners
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
            };

            // Ensure the image loads properly
            croppedImage.crossOrigin = 'anonymous';
            croppedImage.src = cropCanvas.toDataURL('image/png');

        } catch (error) {
            console.error('Error during image resize:', error);
        }
    };

    // Test function for Universal Template System
    window.testUniversalTemplateSystem = function() {
        console.log('\n🧪 TESTING UNIVERSAL TEMPLATE SYSTEM');
        console.log('=====================================');

        // Test 1: Check if UNIVERSAL_TEMPLATE_CONFIG exists
        if (typeof UNIVERSAL_TEMPLATE_CONFIG !== 'undefined') {
            console.log('✅ UNIVERSAL_TEMPLATE_CONFIG is defined');
            console.log('Available purposes:', Object.keys(UNIVERSAL_TEMPLATE_CONFIG));
        } else {
            console.log('❌ UNIVERSAL_TEMPLATE_CONFIG is not defined');
            return;
        }

        // Test 2: Test Benefit template
        if (UNIVERSAL_TEMPLATE_CONFIG['Benefit']) {
            console.log('✅ Benefit template configuration found');
            const benefitConfig = UNIVERSAL_TEMPLATE_CONFIG['Benefit'];
            console.log('Benefit templates count:', benefitConfig.templates.length);
            console.log('Default texts:', benefitConfig.defaultTexts);
        } else {
            console.log('❌ Benefit template configuration not found');
        }

        // Test 3: Test template loading function
        if (typeof loadUniversalTemplatesForPurpose === 'function') {
            console.log('✅ loadUniversalTemplatesForPurpose function exists');
        } else {
            console.log('❌ loadUniversalTemplatesForPurpose function not found');
        }

        // Test 4: Test canvas update function
        if (typeof updateUniversalEditorCanvas === 'function') {
            console.log('✅ updateUniversalEditorCanvas function exists');
        } else {
            console.log('❌ updateUniversalEditorCanvas function not found');
        }

        // Test 5: Test confirm function
        if (typeof window.confirmUniversalTemplateDesign === 'function') {
            console.log('✅ confirmUniversalTemplateDesign function exists');
        } else {
            console.log('❌ confirmUniversalTemplateDesign function not found');
        }

        console.log('\n🎯 To test ANY template (Anniversary, Benefit, Graduation):');
        console.log('1. Select any purpose from the dropdown');
        console.log('2. The system will show the universal template section');
        console.log('3. Click on any template to open the editor');
        console.log('4. Customize the text and click "Confirm Design"');

        console.log('\n🔧 To add a new purpose dynamically:');
        console.log('window.registerNewPurpose("Birthday", {');
        console.log('  defaultTexts: ["HAPPY BIRTHDAY", "John Doe", "Celebrating Life", "Join the Party!"],');
        console.log('  templates: [{ id: "birthday-1", name: "Birthday Template", backgroundColor: "#ff69b4", ... }]');
        console.log('});');

        console.log('\n📊 Available purposes:', window.getAvailablePurposes ? window.getAvailablePurposes() : 'Function not available');
        console.log('✅ ALL purposes now use the same universal system!');

        return true;
    };

    // Demo function to show ANY template in action (Anniversary, Benefit, Graduation)
    window.demoUniversalTemplate = function(purpose = 'Anniversary') {
        console.log(`\n🎬 DEMO: ${purpose} Template`);
        console.log('========================');

        if (!UNIVERSAL_TEMPLATE_CONFIG[purpose]) {
            console.log(`❌ Purpose "${purpose}" not found in universal config`);
            return `Purpose "${purpose}" not available`;
        }

        // Step 1: Set purpose
        localStorage.setItem('selectedPurpose', purpose);

        // Step 2: Show templates for the purpose
        if (typeof showTemplatesForPurpose === 'function') {
            showTemplatesForPurpose(purpose);
            console.log(`✅ ${purpose} templates should now be visible`);
        }

        // Step 3: Auto-select the first template after a delay
        setTimeout(() => {
            const firstTemplate = UNIVERSAL_TEMPLATE_CONFIG[purpose].templates[0];
            if (firstTemplate && typeof selectUniversalTemplate === 'function') {
                selectUniversalTemplate(firstTemplate.id, purpose);
                console.log(`✅ ${purpose} template selected and editor opened`);
                console.log('📝 You can now edit the text and see live preview');
            }
        }, 1000);

        return `Demo started! Check the UI for the ${purpose} template editor.`;
    };

    // Legacy function for backward compatibility
    window.demoBenefitTemplate = function() {
        return window.demoUniversalTemplate('Benefit');
    };

    // Demo function to register a new purpose
    window.demoRegisterBirthdayPurpose = function() {
        console.log('\n🎂 DEMO: Registering Birthday Purpose');
        console.log('====================================');

        const birthdayConfig = {
            defaultTexts: ['HAPPY BIRTHDAY', 'John Doe', 'Celebrating Life', 'Join the Party!'],
            templates: [
                {
                    id: 'birthday-template-1',
                    name: 'Pink Birthday',
                    backgroundColor: '#ff1493',
                    textStyles: [
                        { color: '#ffffff', fontSize: '50px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '2px' },
                        { color: '#ffff00', fontSize: '38px', fontFamily: 'Cursive', fontStyle: 'italic' },
                        { color: '#ffffff', fontSize: '30px', fontFamily: 'Arial', fontWeight: '700' },
                        { color: '#ffff00', fontSize: '26px', fontFamily: 'Arial', fontWeight: '600' }
                    ],
                    textPositions: [
                        { x: 400, y: 100, align: 'center' },
                        { x: 400, y: 160, align: 'center' },
                        { x: 400, y: 220, align: 'center' },
                        { x: 400, y: 280, align: 'center' }
                    ]
                },
                {
                    id: 'birthday-template-2',
                    name: 'Blue Birthday',
                    backgroundColor: '#4169e1',
                    textStyles: [
                        { color: '#ffffff', fontSize: '50px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '2px' },
                        { color: '#ffd700', fontSize: '38px', fontFamily: 'Cursive', fontStyle: 'italic' },
                        { color: '#ffffff', fontSize: '30px', fontFamily: 'Arial', fontWeight: '700' },
                        { color: '#ffd700', fontSize: '26px', fontFamily: 'Arial', fontWeight: '600' }
                    ],
                    textPositions: [
                        { x: 400, y: 100, align: 'center' },
                        { x: 400, y: 160, align: 'center' },
                        { x: 400, y: 220, align: 'center' },
                        { x: 400, y: 280, align: 'center' }
                    ]
                }
            ]
        };

        if (window.registerNewPurpose) {
            const success = window.registerNewPurpose('Birthday', birthdayConfig);
            if (success) {
                console.log('✅ Birthday purpose registered successfully!');
                console.log('📝 You can now select "Birthday" from the purpose dropdown');
                console.log('🎨 Available purposes:', window.getAvailablePurposes());
                return 'Birthday purpose registered! You can now use it in the dropdown.';
            } else {
                console.log('❌ Failed to register Birthday purpose');
                return 'Failed to register Birthday purpose.';
            }
        } else {
            console.log('❌ registerNewPurpose function not available');
            return 'registerNewPurpose function not available.';
        }
    };

    // Auto-run test on page load (for development)
    setTimeout(() => {
        if (window.location.search.includes('test=true')) {
            window.testUniversalTemplateSystem();
        }
    }, 2000);
</script>