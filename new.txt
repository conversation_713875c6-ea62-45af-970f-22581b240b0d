    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Robot<PERSON>, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        /* Black preview area */
        .preview-area {
            width: 100%;
            height: 350px;
            background: #000;
            border-radius: 8px;
            margin-bottom: 30px;
            position: relative;
            background-image:
                linear-gradient(45deg, rgba(255,255,255,0.02) 25%, transparent 25%),
                linear-gradient(-45deg, rgba(255,255,255,0.02) 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, rgba(255,255,255,0.02) 75%),
                linear-gradient(-45deg, transparent 75%, rgba(255,255,255,0.02) 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }

        /* Main content area */
        .content-area {
            padding: 30px;
        }

        /* Two column layout */
        .editor-layout {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        /* Left column - Edit Text */
        .edit-text-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }

        .text-input-group {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .text-input {
            flex: 1;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            transition: border-color 0.2s;
        }

        .text-input:focus {
            outline: none;
            border-color: #0D7EE8;
            box-shadow: 0 0 0 3px rgba(13,126,232,0.1);
        }

        .color-btn {
            width: 36px;
            height: 36px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            background: #ffffff;
            position: relative;
            transition: all 0.2s;
        }

        .color-btn::after {
            content: '▼';
            position: absolute;
            right: 2px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 8px;
            color: #666;
        }

        .color-btn:hover {
            border-color: #0D7EE8;
        }

        /* Color picker dropdown */
        .color-picker-dropdown {
            position: relative;
            display: inline-block;
        }

        .color-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            z-index: 1000;
            display: none;
            padding: 20px;
            width: 420px;
        }

        .color-dropdown.show {
            display: block;
        }

        .color-palette {
            display: grid;
            grid-template-columns: repeat(10, 20px);
            gap: 3px;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }

        .color-swatch {
            width: 20px;
            height: 20px;
            border-radius: 3px;
            cursor: pointer;
            border: 1px solid #ddd;
            transition: all 0.2s ease;
            position: relative;
        }

        .color-swatch:hover {
            transform: scale(1.1);
            border-color: #999;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
            z-index: 1;
        }

        .color-swatch.selected {
            border: 2px solid #333;
            transform: scale(1.05);
        }

        .color-swatch.checkmark {
            background: #00ff00 !important;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #000;
            font-size: 14px;
        }

        /* Advanced Color Picker Components */
        .color-picker-advanced {
            display: flex;
            gap: 15px;
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #e0e0e0;
        }

        .color-gradient-area {
            width: 240px;
            height: 180px;
            position: relative;
            background: linear-gradient(to right, #fff, #ff0000);
            border: 1px solid #ccc;
            cursor: crosshair;
            border-radius: 4px;
        }

        .color-gradient-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, transparent, #000);
            pointer-events: none;
            border-radius: 3px;
        }

        .color-gradient-picker {
            position: absolute;
            width: 12px;
            height: 12px;
            border: 2px solid #fff;
            border-radius: 50%;
            box-shadow: 0 0 4px rgba(0,0,0,0.6);
            transform: translate(-50%, -50%);
            pointer-events: none;
        }

        .hue-slider-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .hue-slider {
            width: 24px;
            height: 180px;
            background: linear-gradient(to bottom,
                #ff0000 0%,
                #ffff00 16.66%,
                #00ff00 33.33%,
                #00ffff 50%,
                #0000ff 66.66%,
                #ff00ff 83.33%,
                #ff0000 100%);
            border: 1px solid #ccc;
            position: relative;
            cursor: pointer;
            border-radius: 4px;
        }

        .hue-slider-handle {
            position: absolute;
            width: 28px;
            height: 8px;
            background: #fff;
            border: 1px solid #333;
            left: -2px;
            transform: translateY(-50%);
            pointer-events: none;
            border-radius: 2px;
        }

        .color-preview-section {
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: center;
        }

        .color-preview-large {
            width: 60px;
            height: 60px;
            border: 1px solid #ccc;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .color-picker-buttons {
            display: flex;
            gap: 10px;
            flex-direction: column;
        }

        .color-picker-btn-small {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: #fff;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s;
            min-width: 60px;
        }

        .color-picker-btn-small:hover {
            background: #f8f9fa;
            border-color: #999;
        }

        .color-picker-btn-small.cancel {
            color: #dc3545;
            border-color: #dc3545;
        }

        .color-picker-btn-small.cancel:hover {
            background: #f8d7da;
        }

        .color-picker-btn-small.choose {
            color: #28a745;
            border-color: #28a745;
            font-weight: 600;
        }

        .color-picker-btn-small.choose:hover {
            background: #d4edda;
        }

        /* Font Settings Modal */
        .font-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            z-index: 2000;
            display: none;
            align-items: center;
            justify-content: center;
        }

        .font-modal.show {
            display: flex;
        }

        .font-modal-content {
            background: white;
            border-radius: 12px;
            padding: 30px;
            width: 500px;
            max-width: 90%;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .font-modal-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 25px;
            text-align: left;
        }

        .font-setting-row {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            gap: 15px;
        }

        .font-setting-label {
            width: 120px;
            font-weight: 600;
            color: #333;
            font-size: 16px;
        }

        .font-setting-control {
            flex: 1;
        }

        .font-dropdown, .font-size-dropdown {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }

        .font-dropdown:focus, .font-size-dropdown:focus {
            outline: none;
            border-color: #0D7EE8;
            box-shadow: 0 0 0 3px rgba(13,126,232,0.1);
        }

        .shadow-checkbox {
            width: 20px;
            height: 20px;
            cursor: pointer;
        }

        .shadow-width-container {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .shadow-width-slider {
            flex: 1;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
            cursor: pointer;
        }

        .shadow-width-value {
            min-width: 30px;
            text-align: center;
            font-weight: 600;
            color: #666;
        }

        .apply-all-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 25px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .apply-all-checkbox {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        .apply-all-label {
            font-weight: 500;
            color: #333;
            cursor: pointer;
        }

        .font-modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .font-modal-btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            min-width: 120px;
        }

        .font-modal-btn.apply {
            background: #007bff;
            color: white;
        }

        .font-modal-btn.apply:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .font-modal-btn.cancel {
            background: #007bff;
            color: white;
        }

        .font-modal-btn.cancel:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .settings-btn {
            width: 36px;
            height: 36px;
            border: 1px solid #ddd;
            background: #f8f9fa;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            font-size: 14px;
        }

        .settings-btn:hover {
            background: #e9ecef;
            border-color: #0D7EE8;
        }

        /* Right column - Customize Design */
        .customize-design-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }

        .background-graphic-btn {
            width: 100%;
            background: #0D7EE8;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .background-graphic-btn:hover {
            background: #0A6AC7;
            transform: translateY(-1px);
        }

        .upload-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e0e0e0;
        }

        .upload-title {
            font-size: 16px;
            font-weight: 500;
            color: #666;
            margin-bottom: 15px;
        }

        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
            background: #fafafa;
        }

        .upload-area:hover {
            border-color: #0D7EE8;
            background-color: rgba(13,126,232,0.05);
        }

        .upload-icon {
            width: 60px;
            height: 60px;
            background: #999;
            border-radius: 8px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .upload-text {
            color: #333;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .upload-subtext {
            color: #999;
            font-size: 13px;
        }

        /* Bottom action buttons */
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .action-btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            min-width: 150px;
        }

        .btn-back {
            background: #6c757d;
            color: white;
        }

        .btn-back:hover {
            background: #545b62;
            transform: translateY(-1px);
        }

        .btn-reset {
            background: #ffc107;
            color: #212529;
        }

        .btn-reset:hover {
            background: #e0a800;
            transform: translateY(-1px);
        }

        .btn-confirm {
            background: #007bff;
            color: white;
        }

        .btn-confirm:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .editor-layout {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .action-buttons {
                flex-direction: column;
                gap: 10px;
            }

            .action-btn {
                width: 100%;
            }
        }
    </style>
    <div class="container">
        <!-- Black preview area -->
        <div class="content-area">
            <div class="preview-area"></div>

            <!-- Two column editor layout -->
            <div class="editor-layout">
                <!-- Left column - Edit Text -->
                <div class="edit-text-section">
                    <h3 class="section-title">Edit Text</h3>

                    <div class="text-input-group">
                        <input type="text" class="text-input" placeholder="Enter text">
                        <div class="color-picker-dropdown">
                            <button class="color-btn" data-line="1"></button>
                            <div class="color-dropdown" id="colorDropdown1">
                                <div class="color-palette" id="colorPalette1"></div>
                                <div class="color-picker-advanced">
                                    <div class="color-gradient-area" id="colorGradient1">
                                        <div class="color-gradient-picker" id="colorPicker1"></div>
                                    </div>
                                    <div class="hue-slider-container">
                                        <div class="hue-slider" id="hueSlider1">
                                            <div class="hue-slider-handle" id="hueHandle1"></div>
                                        </div>
                                    </div>
                                    <div class="color-preview-section">
                                        <div class="color-preview-large" id="colorPreview1" style="background-color: #ff0000;"></div>
                                        <div class="color-picker-buttons">
                                            <button type="button" class="color-picker-btn-small cancel" onclick="cancelColorPicker(1)">cancel</button>
                                            <button type="button" class="color-picker-btn-small choose" onclick="chooseColor(1)">choose</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="settings-btn">⚙️</button>
                    </div>

                    <div class="text-input-group">
                        <input type="text" class="text-input" placeholder="Enter text">
                        <div class="color-picker-dropdown">
                            <button class="color-btn" data-line="2"></button>
                            <div class="color-dropdown" id="colorDropdown2">
                                <div class="color-palette" id="colorPalette2"></div>
                                <div class="color-picker-advanced">
                                    <div class="color-gradient-area" id="colorGradient2">
                                        <div class="color-gradient-picker" id="colorPicker2"></div>
                                    </div>
                                    <div class="hue-slider-container">
                                        <div class="hue-slider" id="hueSlider2">
                                            <div class="hue-slider-handle" id="hueHandle2"></div>
                                        </div>
                                    </div>
                                    <div class="color-preview-section">
                                        <div class="color-preview-large" id="colorPreview2" style="background-color: #ff0000;"></div>
                                        <div class="color-picker-buttons">
                                            <button type="button" class="color-picker-btn-small cancel" onclick="cancelColorPicker(2)">cancel</button>
                                            <button type="button" class="color-picker-btn-small choose" onclick="chooseColor(2)">choose</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="settings-btn">⚙️</button>
                    </div>

                    <div class="text-input-group">
                        <input type="text" class="text-input" placeholder="Enter text">
                        <div class="color-picker-dropdown">
                            <button class="color-btn" data-line="3"></button>
                            <div class="color-dropdown" id="colorDropdown3">
                                <div class="color-palette" id="colorPalette3"></div>
                                <div class="color-picker-advanced">
                                    <div class="color-gradient-area" id="colorGradient3">
                                        <div class="color-gradient-picker" id="colorPicker3"></div>
                                    </div>
                                    <div class="hue-slider-container">
                                        <div class="hue-slider" id="hueSlider3">
                                            <div class="hue-slider-handle" id="hueHandle3"></div>
                                        </div>
                                    </div>
                                    <div class="color-preview-section">
                                        <div class="color-preview-large" id="colorPreview3" style="background-color: #ff0000;"></div>
                                        <div class="color-picker-buttons">
                                            <button type="button" class="color-picker-btn-small cancel" onclick="cancelColorPicker(3)">cancel</button>
                                            <button type="button" class="color-picker-btn-small choose" onclick="chooseColor(3)">choose</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="settings-btn">⚙️</button>
                    </div>

                    <div class="text-input-group">
                        <input type="text" class="text-input" placeholder="Enter text">
                        <div class="color-picker-dropdown">
                            <button class="color-btn" data-line="4"></button>
                            <div class="color-dropdown" id="colorDropdown4">
                                <div class="color-palette" id="colorPalette4"></div>
                                <div class="color-picker-advanced">
                                    <div class="color-gradient-area" id="colorGradient4">
                                        <div class="color-gradient-picker" id="colorPicker4"></div>
                                    </div>
                                    <div class="hue-slider-container">
                                        <div class="hue-slider" id="hueSlider4">
                                            <div class="hue-slider-handle" id="hueHandle4"></div>
                                        </div>
                                    </div>
                                    <div class="color-preview-section">
                                        <div class="color-preview-large" id="colorPreview4" style="background-color: #ff0000;"></div>
                                        <div class="color-picker-buttons">
                                            <button type="button" class="color-picker-btn-small cancel" onclick="cancelColorPicker(4)">cancel</button>
                                            <button type="button" class="color-picker-btn-small choose" onclick="chooseColor(4)">choose</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="settings-btn">⚙️</button>
                    </div>
                </div>

                <!-- Right column - Customize Design -->
                <div class="customize-design-section">
                    <h3 class="section-title">Customize Design</h3>

                    <button class="background-graphic-btn">Background Graphic</button>

                    <div class="upload-section">
                        <div class="upload-title">Upload Your Photo</div>
                        <div class="upload-area">
                            <div class="upload-icon">📷</div>
                            <div class="upload-text">Click to upload your photo</div>
                            <div class="upload-subtext">Accepted formats: JPG, PNG. High resolution recommended.</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom action buttons -->
            <div class="action-buttons">
                <button class="action-btn btn-back">Back to Templates</button>
                <button class="action-btn btn-reset">Reset All Fields</button>
                <button class="action-btn btn-confirm">Confirm Design</button>
            </div>
        </div>
    </div>

    <!-- Font Settings Modal -->
    <div class="font-modal" id="fontModal">
        <div class="font-modal-content">
            <h3 class="font-modal-title">Font Settings</h3>

            <div class="font-setting-row">
                <div class="font-setting-label">Font Family</div>
                <div class="font-setting-control">
                    <select id="modalFontSelect" class="font-dropdown">
                        <option value="Mouse Memoirs" style="font-family: 'Mouse Memoirs';">Mouse Memoirs</option>
                        <option value="Domine" style="font-family: Domine;">Domine</option>
                        <option value="Baloo Tamma" style="font-family: 'Baloo Tamma';">Baloo Tamma</option>
                        <option value="Courgette" style="font-family: Courgette;">Courgette</option>
                        <option value="Oswald" style="font-family: Oswald;">Oswald</option>
                        <option value="Kaushan Script" style="font-family: 'Kaushan Script';">Kaushan Script</option>
                        <option value="Alfa Slab One" style="font-family: 'Alfa Slab One';">Alfa Slab One</option>
                        <option value="Montserrat" style="font-family: Montserrat;">Montserrat</option>
                        <option value="Yellowtail" style="font-family: Yellowtail;">Yellowtail</option>
                        <option value="Paytone One" style="font-family: 'Paytone One';">Paytone One</option>
                        <option value="Indie Flower" style="font-family: 'Indie Flower';">Indie Flower</option>
                        <option value="Dancing Script" style="font-family: 'Dancing Script';">Dancing Script</option>
                        <option value="Anton" style="font-family: Anton;">Anton</option>
                        <option value="Luckiest Guy" style="font-family: 'Luckiest Guy';">Luckiest Guy</option>
                        <option value="Permanent Marker" style="font-family: 'Permanent Marker';">Permanent Marker</option>
                        <option value="Sniglet" style="font-family: Sniglet;">Sniglet</option>
                        <option value="Lobster" style="font-family: Lobster;">Lobster</option>
                        <option value="Arial" style="font-family: Arial;">Arial</option>
                    </select>
                </div>
            </div>

            <div class="font-setting-row">
                <div class="font-setting-label">Font Size</div>
                <div class="font-setting-control">
                    <select id="modalFontSize" class="font-size-dropdown">
                        <!-- Options will be populated by JavaScript -->
                    </select>
                </div>
            </div>

            <div class="font-setting-row">
                <div class="font-setting-label">Shadow?</div>
                <div class="font-setting-control">
                    <input type="checkbox" id="modalShadow" class="shadow-checkbox" checked>
                </div>
            </div>

            <div class="font-setting-row">
                <div class="font-setting-label">Shadow Width</div>
                <div class="font-setting-control">
                    <div class="shadow-width-container">
                        <input type="range" id="modalShadowWidth" class="shadow-width-slider" min="0" max="10" value="4">
                        <div class="shadow-width-value" id="shadowWidthValue">4</div>
                    </div>
                </div>
            </div>

            <div class="apply-all-container">
                <input type="checkbox" id="applyToAll" class="apply-all-checkbox">
                <label for="applyToAll" class="apply-all-label">Apply to all text areas</label>
            </div>

            <div class="font-modal-buttons">
                <button type="button" class="font-modal-btn apply" onclick="applyFontSettings()">Apply</button>
                <button type="button" class="font-modal-btn cancel" onclick="closeFontModal()">Cancel</button>
            </div>
        </div>
    </div>

    <script>
        // Color palette data
        const colorPalette = [
            // Row 1 - Blacks and grays
            '#000000', '#333333', '#666666', '#999999', '#BBBBBB', '#CCCCCC', '#DDDDDD', '#EEEEEE', '#F5F5F5', '#FFFFFF',
            // Row 2 - Bright colors
            '#FF0000', '#FF6600', '#FFFF00', '#00FF00', '#00FFFF', '#0066FF', '#0000FF', '#6600FF', '#FF00FF', '#FF0066',
            // Row 3 - Light pastels
            '#FFE6E6', '#FFE6CC', '#FFFFCC', '#E6FFE6', '#E6FFFF', '#E6F2FF', '#E6E6FF', '#F2E6FF', '#FFE6FF', '#FFE6F2',
            // Row 4 - Medium pastels
            '#FFCCCC', '#FFCC99', '#FFFF99', '#CCFFCC', '#CCFFFF', '#CCE5FF', '#CCCCFF', '#E5CCFF', '#FFCCFF', '#FFCCE5',
            // Row 5 - Vibrant colors
            '#FF6600', '#99FF00', '#00FF99', '#0099FF', '#9900FF', '#FF0099', '#CC3300', '#FF3300', '#FF6600', '#CC6600',
            // Row 6 - Dark colors
            '#663300', '#666600', '#003366', '#330066', '#660033', '#330000', '#003300', '#000033', '#330033', '#663333'
        ];

        let currentColorPicker = null;
        let currentHue = 0;
        let currentSaturation = 100;
        let currentLightness = 50;

        // Initialize color pickers
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all color palettes
            for (let i = 1; i <= 4; i++) {
                initializeColorPalette(i);
                initializeAdvancedColorPicker(i);
            }

            // Upload area click handler
            const uploadArea = document.querySelector('.upload-area');
            uploadArea.addEventListener('click', function() {
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = 'image/jpeg,image/png';
                fileInput.style.display = 'none';

                fileInput.addEventListener('change', function(e) {
                    if (e.target.files && e.target.files[0]) {
                        const file = e.target.files[0];
                        const uploadIcon = uploadArea.querySelector('.upload-icon');
                        const uploadText = uploadArea.querySelector('.upload-text');
                        const uploadSubtext = uploadArea.querySelector('.upload-subtext');

                        uploadIcon.textContent = '✓';
                        uploadIcon.style.background = '#28a745';
                        uploadText.textContent = file.name;
                        uploadSubtext.textContent = 'File uploaded successfully. Click to change.';
                    }
                });

                document.body.appendChild(fileInput);
                fileInput.click();
                document.body.removeChild(fileInput);
            });

            // Color button handlers
            const colorBtns = document.querySelectorAll('.color-btn');
            colorBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const lineNumber = btn.getAttribute('data-line');
                    toggleColorPicker(lineNumber);
                });
            });

            // Close color picker when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.color-picker-dropdown')) {
                    closeAllColorPickers();
                }
            });

            // Settings button handlers
            const settingsBtns = document.querySelectorAll('.settings-btn');
            settingsBtns.forEach((btn, index) => {
                btn.addEventListener('click', function() {
                    openFontModal(index + 1);
                });
            });

            // Initialize font modal
            initializeFontModal();

            // Other button handlers
            initializeOtherButtons();
        });

        function initializeColorPalette(lineNumber) {
            const palette = document.getElementById(`colorPalette${lineNumber}`);
            palette.innerHTML = '';

            // Add checkmark swatch first
            const checkmarkSwatch = document.createElement('div');
            checkmarkSwatch.className = 'color-swatch checkmark';
            checkmarkSwatch.textContent = '✓';
            checkmarkSwatch.addEventListener('click', () => selectPaletteColor(lineNumber, '#00ff00'));
            palette.appendChild(checkmarkSwatch);

            // Add regular color swatches
            colorPalette.forEach(color => {
                const swatch = document.createElement('div');
                swatch.className = 'color-swatch';
                swatch.style.backgroundColor = color;
                swatch.addEventListener('click', () => selectPaletteColor(lineNumber, color));
                palette.appendChild(swatch);
            });
        }

        function initializeAdvancedColorPicker(lineNumber) {
            const gradientArea = document.getElementById(`colorGradient${lineNumber}`);
            const hueSlider = document.getElementById(`hueSlider${lineNumber}`);
            const colorPreview = document.getElementById(`colorPreview${lineNumber}`);
            const colorPicker = document.getElementById(`colorPicker${lineNumber}`);
            const hueHandle = document.getElementById(`hueHandle${lineNumber}`);

            // Initialize with red
            updateColorPreview(lineNumber, '#ff0000');

            // Gradient area click handler
            gradientArea.addEventListener('mousedown', function(e) {
                currentColorPicker = lineNumber;
                updateGradientPicker(e, lineNumber);

                const mouseMoveHandler = (e) => updateGradientPicker(e, lineNumber);
                const mouseUpHandler = () => {
                    document.removeEventListener('mousemove', mouseMoveHandler);
                    document.removeEventListener('mouseup', mouseUpHandler);
                };

                document.addEventListener('mousemove', mouseMoveHandler);
                document.addEventListener('mouseup', mouseUpHandler);
            });

            // Hue slider click handler
            hueSlider.addEventListener('mousedown', function(e) {
                currentColorPicker = lineNumber;
                updateHueSlider(e, lineNumber);

                const mouseMoveHandler = (e) => updateHueSlider(e, lineNumber);
                const mouseUpHandler = () => {
                    document.removeEventListener('mousemove', mouseMoveHandler);
                    document.removeEventListener('mouseup', mouseUpHandler);
                };

                document.addEventListener('mousemove', mouseMoveHandler);
                document.addEventListener('mouseup', mouseUpHandler);
            });
        }

        function toggleColorPicker(lineNumber) {
            closeAllColorPickers();
            const dropdown = document.getElementById(`colorDropdown${lineNumber}`);
            dropdown.classList.add('show');
            currentColorPicker = lineNumber;
        }

        function closeAllColorPickers() {
            for (let i = 1; i <= 4; i++) {
                const dropdown = document.getElementById(`colorDropdown${i}`);
                dropdown.classList.remove('show');
            }
            currentColorPicker = null;
        }

        function selectPaletteColor(lineNumber, color) {
            updateColorPreview(lineNumber, color);
            const colorBtn = document.querySelector(`[data-line="${lineNumber}"]`);
            colorBtn.style.backgroundColor = color;
            closeAllColorPickers();
        }

        function updateGradientPicker(e, lineNumber) {
            const gradientArea = document.getElementById(`colorGradient${lineNumber}`);
            const rect = gradientArea.getBoundingClientRect();
            const x = Math.max(0, Math.min(rect.width, e.clientX - rect.left));
            const y = Math.max(0, Math.min(rect.height, e.clientY - rect.top));

            const colorPicker = document.getElementById(`colorPicker${lineNumber}`);
            colorPicker.style.left = x + 'px';
            colorPicker.style.top = y + 'px';

            // Calculate color based on position
            const saturation = (x / rect.width) * 100;
            const lightness = 100 - (y / rect.height) * 100;
            const color = `hsl(${currentHue}, ${saturation}%, ${lightness}%)`;

            updateColorPreview(lineNumber, color);
        }

        function updateHueSlider(e, lineNumber) {
            const hueSlider = document.getElementById(`hueSlider${lineNumber}`);
            const rect = hueSlider.getBoundingClientRect();
            const y = Math.max(0, Math.min(rect.height, e.clientY - rect.top));

            const hueHandle = document.getElementById(`hueHandle${lineNumber}`);
            hueHandle.style.top = y + 'px';

            currentHue = (y / rect.height) * 360;

            // Update gradient background
            const gradientArea = document.getElementById(`colorGradient${lineNumber}`);
            const hueColor = `hsl(${currentHue}, 100%, 50%)`;
            gradientArea.style.background = `linear-gradient(to right, #fff, ${hueColor})`;

            // Update current color
            const color = `hsl(${currentHue}, ${currentSaturation}%, ${currentLightness}%)`;
            updateColorPreview(lineNumber, color);
        }

        function updateColorPreview(lineNumber, color) {
            const colorPreview = document.getElementById(`colorPreview${lineNumber}`);
            colorPreview.style.backgroundColor = color;
        }

        function cancelColorPicker(lineNumber) {
            closeAllColorPickers();
        }

        function chooseColor(lineNumber) {
            const colorPreview = document.getElementById(`colorPreview${lineNumber}`);
            const color = colorPreview.style.backgroundColor;
            const colorBtn = document.querySelector(`[data-line="${lineNumber}"]`);
            colorBtn.style.backgroundColor = color;
            closeAllColorPickers();
        }

        // Font Modal Variables
        let currentFontLineNumber = null;

        function initializeFontModal() {
            // Populate font sizes
            const fontSizeSelect = document.getElementById('modalFontSize');
            const fontSizes = [8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 36, 40, 44, 48, 54, 60, 66, 72, 80, 88, 96];

            fontSizes.forEach(size => {
                const option = document.createElement('option');
                option.value = size;
                option.textContent = size;
                if (size === 10) option.selected = true;
                fontSizeSelect.appendChild(option);
            });

            // Shadow width slider handler
            const shadowWidthSlider = document.getElementById('modalShadowWidth');
            const shadowWidthValue = document.getElementById('shadowWidthValue');

            shadowWidthSlider.addEventListener('input', function() {
                shadowWidthValue.textContent = this.value;
            });

            // Close modal when clicking outside
            document.getElementById('fontModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeFontModal();
                }
            });

            // Prevent modal content clicks from closing modal
            document.querySelector('.font-modal-content').addEventListener('click', function(e) {
                e.stopPropagation();
            });
        }

        function openFontModal(lineNumber) {
            currentFontLineNumber = lineNumber;
            const modal = document.getElementById('fontModal');

            // Reset form to defaults
            document.getElementById('modalFontSelect').value = 'Mouse Memoirs';
            document.getElementById('modalFontSize').value = '10';
            document.getElementById('modalShadow').checked = true;
            document.getElementById('modalShadowWidth').value = '4';
            document.getElementById('shadowWidthValue').textContent = '4';
            document.getElementById('applyToAll').checked = false;

            modal.classList.add('show');

            // Close any open color pickers
            closeAllColorPickers();
        }

        function closeFontModal() {
            const modal = document.getElementById('fontModal');
            modal.classList.remove('show');
            currentFontLineNumber = null;
        }

        function applyFontSettings() {
            if (!currentFontLineNumber) return;

            const fontFamily = document.getElementById('modalFontSelect').value;
            const fontSize = document.getElementById('modalFontSize').value;
            const hasShadow = document.getElementById('modalShadow').checked;
            const shadowWidth = document.getElementById('modalShadowWidth').value;
            const applyToAll = document.getElementById('applyToAll').checked;

            const settings = {
                fontFamily: fontFamily,
                fontSize: fontSize + 'px',
                hasShadow: hasShadow,
                shadowWidth: shadowWidth + 'px'
            };

            if (applyToAll) {
                // Apply to all text inputs
                const textInputs = document.querySelectorAll('.text-input');
                textInputs.forEach(input => {
                    applyFontToInput(input, settings);
                });
                console.log('Font settings applied to all text areas:', settings);
            } else {
                // Apply to specific text input
                const textInput = document.querySelectorAll('.text-input')[currentFontLineNumber - 1];
                applyFontToInput(textInput, settings);
                console.log(`Font settings applied to text line ${currentFontLineNumber}:`, settings);
            }

            closeFontModal();
        }

        function applyFontToInput(input, settings) {
            input.style.fontFamily = settings.fontFamily;
            input.style.fontSize = settings.fontSize;

            if (settings.hasShadow) {
                input.style.textShadow = `${settings.shadowWidth} ${settings.shadowWidth} ${settings.shadowWidth} rgba(0,0,0,0.5)`;
            } else {
                input.style.textShadow = 'none';
            }
        }

        function initializeOtherButtons() {
            // Background graphic button handler
            document.querySelector('.background-graphic-btn').addEventListener('click', function() {
                alert('Background Graphic Selection\n\nThis would open a dialog with:\n- Image background options\n- Color background picker\n- Gradient options');
            });

            // Action button handlers
            document.querySelector('.btn-back').addEventListener('click', function() {
                alert('Going back to template selection...');
            });

            document.querySelector('.btn-reset').addEventListener('click', function() {
                // Clear all text inputs
                document.querySelectorAll('.text-input').forEach(input => {
                    input.value = '';
                });

                // Reset color buttons to white
                document.querySelectorAll('.color-btn').forEach(btn => {
                    btn.style.backgroundColor = '#ffffff';
                });

                // Reset upload area
                const uploadArea = document.querySelector('.upload-area');
                const uploadIcon = uploadArea.querySelector('.upload-icon');
                const uploadText = uploadArea.querySelector('.upload-text');
                const uploadSubtext = uploadArea.querySelector('.upload-subtext');

                uploadIcon.textContent = '📷';
                uploadIcon.style.background = '#999';
                uploadText.textContent = 'Click to upload your photo';
                uploadSubtext.textContent = 'Accepted formats: JPG, PNG. High resolution recommended.';

                alert('All fields have been reset!');
            });

            document.querySelector('.btn-confirm').addEventListener('click', function() {
                // Collect form data
                const textInputs = document.querySelectorAll('.text-input');
                const colorBtns = document.querySelectorAll('.color-btn');

                let formData = {
                    texts: [],
                    colors: []
                };

                textInputs.forEach((input, index) => {
                    formData.texts.push(input.value);
                    formData.colors.push(colorBtns[index].style.backgroundColor || '#ffffff');
                });

                console.log('Form data:', formData);
                alert('Design confirmed!\n\nThis would submit the design with:\n' +
                      'Text lines: ' + formData.texts.filter(t => t).length + '\n' +
                      'Colors set: ' + formData.colors.length);
            });
        }
    </script>

<script>
            // Settings button handlers
            const settingsBtns = document.querySelectorAll('.settings-btn');
            settingsBtns.forEach((btn, index) => {
                btn.addEventListener('click', function() {
                    console.log('Settings clicked for input', index + 1);
                    alert('Font settings for text line ' + (index + 1) + '\n\nThis would open a font settings modal with:\n- Font family selection\n- Font size options\n- Text shadow settings\n- Apply to all option');
                });
            });

            // Background graphic button handler
            document.querySelector('.background-graphic-btn').addEventListener('click', function() {
                console.log('Background graphic clicked');
                alert('Background Graphic Selection\n\nThis would open a dialog with:\n- Image background options\n- Color background picker\n- Gradient options');
            });

            // Action button handlers
            document.querySelector('.btn-back').addEventListener('click', function() {
                console.log('Back to templates');
                alert('Going back to template selection...');
            });

            document.querySelector('.btn-reset').addEventListener('click', function() {
                console.log('Reset all fields');

                // Clear all text inputs
                document.querySelectorAll('.text-input').forEach(input => {
                    input.value = '';
                });

                // Reset color buttons to white
                document.querySelectorAll('.color-btn').forEach(btn => {
                    btn.style.backgroundColor = '#ffffff';
                });

                // Reset upload area
                const uploadArea = document.querySelector('.upload-area');
                const uploadIcon = uploadArea.querySelector('.upload-icon');
                const uploadText = uploadArea.querySelector('.upload-text');
                const uploadSubtext = uploadArea.querySelector('.upload-subtext');

                uploadIcon.textContent = '📷';
                uploadIcon.style.background = '#999';
                uploadText.textContent = 'Click to upload your photo';
                uploadSubtext.textContent = 'Accepted formats: JPG, PNG. High resolution recommended.';

                alert('All fields have been reset!');
            });

            document.querySelector('.btn-confirm').addEventListener('click', function() {
                console.log('Confirm design');

                // Collect form data
                const textInputs = document.querySelectorAll('.text-input');
                const colorBtns = document.querySelectorAll('.color-btn');

                let formData = {
                    texts: [],
                    colors: []
                };

                textInputs.forEach((input, index) => {
                    formData.texts.push(input.value);
                    formData.colors.push(colorBtns[index].style.backgroundColor || '#ffffff');
                });

                console.log('Form data:', formData);
                alert('Design confirmed!\n\nThis would submit the design with:\n' +
                      'Text lines: ' + formData.texts.filter(t => t).length + '\n' +
                      'Colors set: ' + formData.colors.length);
            });
        });
</script>